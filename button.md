```py
def create_payment_buttons(lang, ton_price, expected_message, callback_data="confirm_premium_payment_sent"):
    """Create standardized payment buttons for TON payments"""
    ton_link = generate_ton_payment_link(TON_PAYMENT_ADDRESS, ton_price, expected_message)
    tonkeeper_link = generate_ton_wallet_link(TON_PAYMENT_ADDRESS, ton_price, expected_message)

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(
                text="🚀 Pay with TON Wallet" if lang == 'en' else "🚀 Оплатить TON кошельком",
                url=ton_link
            )],
            [InlineKeyboardButton(
                text="💎 Pay with Tonkeeper" if lang == 'en' else "💎 Оплатить Tonkeeper",
                url=tonkeeper_link
            )],
            [InlineKeyboardButton(
                text="✅ I have sent the payment" if lang == 'en' else "✅ Я отправил платёж",
                callback_data=callback_data
            )],
            [InlineKeyboardButton(
                text="❌ Cancel" if lang == 'en' else "❌ Отмена",
                callback_data="cancel_buy"
            )]
        ]
    )


def generate_ton_payment_link(wallet_address, amount, message=""):
    """Generate TON payment link with auto-filled amount and message"""
    import urllib.parse

    base_url = "ton://transfer/"
    params = {
        "amount": str(int(amount * 1000000000))  # Convert to nanotons
    }

    if message:
        params["text"] = message

    query_string = urllib.parse.urlencode(params)
    return f"{base_url}{wallet_address}?{query_string}"

def generate_ton_wallet_link(wallet_address, amount, message=""):
    """Generate alternative TON wallet link format"""
    import urllib.parse

    params = {
        "amount": str(int(amount * 1000000000)),  # Convert to nanotons
    }

    if message:
        params["text"] = message

    query_string = urllib.parse.urlencode(params)
    return f"https://app.tonkeeper.com/transfer/{wallet_address}?{query_string}"
```
