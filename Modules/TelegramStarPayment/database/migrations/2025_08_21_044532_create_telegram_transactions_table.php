<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telegram_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('currency');
            $table->integer('total_amount');
            $table->text('invoice_payload');
            $table->string('telegram_payment_charge_id');
            $table->string('provider_payment_charge_id');
            $table->string('status')->default('pending');

            $table->string('transaction_hash');
            $table->unsignedBigInteger('user_id');

            $table->index('transaction_hash');
            $table->index('user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telegram_transactions');
    }
};
