<?php

namespace Modules\TelegramStarPayment\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TelegramTransaction extends Model
{
    use HasFactory;

    protected $table = 'telegram_transactions';

    protected $fillable = [
        'currency',
        'total_amount',
        'invoice_payload',
        'telegram_payment_charge_id',
        'provider_payment_charge_id',
        'transaction_hash',
        'user_id',
        'status',
    ];
}
