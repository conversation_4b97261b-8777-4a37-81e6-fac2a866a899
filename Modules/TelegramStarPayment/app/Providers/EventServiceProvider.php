<?php

namespace Modules\TelegramStarPayment\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\TelegramBot\Events\TelegramPaymentSuccessful;
use Modules\TelegramBot\Events\TelegramPreCheckoutReceived;
use Modules\TelegramStarPayment\Listeners\PreCheckoutListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event handler mappings for the application.
     *
     * @var array<string, array<int, string>>
     */
    protected $listen = [
        TelegramPreCheckoutReceived::class => [
            PreCheckoutListener::class,
        ],
        TelegramPaymentSuccessful::class => [
        ],
    ];

    /**
     * Indicates if events should be discovered.
     *
     * @var bool
     */
    protected static $shouldDiscoverEvents = true;

    /**
     * Configure the proper event listeners for email verification.
     */
    protected function configureEmailVerification(): void {}
}
