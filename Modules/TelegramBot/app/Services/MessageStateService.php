<?php

namespace Modules\TelegramBot\Services;

use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Models\UserMessageState;

class MessageStateService
{
    public function __construct()
    {
        // Initialization if needed
    }

    public function getUserStateRecord(TelegramUser $user): ?UserMessageState
    {
        return UserMessageState::firstOrCreate(
            ['telegram_user_id' => $user->tele_id],
            ['state' => 'default', 'data' => null]
        );
    }

    /**
     * Get user state from cache
     */
    public function getUserState(TelegramUser $user): string
    {
        return $this->getUserStateRecord($user)->state;
    }

    /**
     * Set user state in cache
     */
    public function setUserState(TelegramUser $user, string $state): void
    {
        $userState = $this->getUserStateRecord($user);
        $userState->state = $state;
        $userState->save();
    }

    /**
     * Clear user state
     */
    public function clearUserState(TelegramUser $user): void
    {
        $userState = $this->getUserStateRecord($user);
        $userState->state = 'default';
        $userState->data = null;
        $userState->save();
    }
}
