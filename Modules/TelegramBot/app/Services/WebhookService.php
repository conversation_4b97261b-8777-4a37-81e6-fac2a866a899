<?php

namespace Modules\TelegramBot\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;

class WebhookService
{
    protected Api $telegram;

    public function __construct(Api $telegram)
    {
        $this->telegram = $telegram;
    }

    /**
     * Verify webhook request authenticity
     */
    public function verifyWebhookRequest(Request $request): bool
    {
        $secret = env('TELEGRAM_WEBHOOK_SECRET');

        if (! $secret) {
            Log::warning('Webhook secret not configured, skipping verification');

            return true;
        }

        $headerToken = $request->header('X-Telegram-Bot-Api-Secret-Token');

        if (! $headerToken) {
            Log::warning('Missing X-Telegram-Bot-Api-Secret-Token header');

            return false;
        }

        $isValid = $secret === $headerToken;

        if (! $isValid) {
            Log::warning('Invalid webhook secret token', [
                'expected_length' => strlen($secret),
                'received_length' => strlen($headerToken),
                'expected_secret' => substr($secret, 0, 10).'...',
                'received_secret' => substr($headerToken, 0, 10).'...',
            ]);
        }

        return $isValid;
    }

    /**
     * Determine the type of update
     */
    public function getUpdateType(array $update): string
    {
        $types = [
            'message',
            'edited_message',
            'channel_post',
            'edited_channel_post',
            'inline_query',
            'chosen_inline_result',
            'callback_query',
            'shipping_query',
            'pre_checkout_query',
            'successful_payment',
            'poll',
            'poll_answer',
            'my_chat_member',
            'chat_member',
            'chat_join_request',
        ];

        foreach ($types as $type) {
            if (isset($update[$type])) {
                return $type;
            }
        }

        return 'unknown';
    }
}
