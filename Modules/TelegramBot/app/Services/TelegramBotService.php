<?php

namespace Modules\TelegramBot\Services;

use Modules\TelegramBot\Models\TelegramBot;
use Modules\TelegramClient\Services\TelegramBotClient;
use App\Helpers\DiscordLogHelper;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Artisan;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramBot\Interfaces\StateHandleInterface;

class TelegramBotService
{
    public function __construct() {}

    public function createBotFromToken(string $token, string $module = 'default')
    {
        $client = new TelegramBotClient($token);
        $botInfo = $client->getBotInfo();
        $settings = [
            'module' => $module,
        ];

        TelegramBot::create([
            'token' => $token,
            'name' => $botInfo['data']['username'],
            'settings' => json_encode($settings),
        ]);
    }

    public function parseCommand(string $text): array
    {
        $text = trim($text);
        $parts = explode(' ', $text);
        $params = [];

        foreach ($parts as $part) {
            if (strpos($part, '=') !== false) {
                [$key, $value] = explode('=', $part, 2);
                $params[trim($key)] = trim($value);
            }
        }

        return $params;
    }

    public function updateBotModule(TelegramBot $bot, string $module): void
    {
        $currentSettings = json_decode($bot->settings, true) ?? [];
        $currentSettings['module'] = $module;

        $bot->settings = json_encode($currentSettings);
        $bot->save();
    }

    public function getModuleName(): string
    {
        $tenant = app('currentTenant');
        $settings = json_decode($tenant->settings, true);

        return $settings['module'] === 'default' ? 'TelegramBot' : $settings['module'];
    }

    public function commandLoader(string $command): ?CommandInterface
    {
        $className = Str::studly($command, '_').'Command';
        $fqcn = 'Modules\\'.$this->getModuleName()."\\TelegramCommands\\{$className}";

        if (! class_exists($fqcn)) {
            $fqcn = "Modules\\TelegramBot\\TelegramCommands\\{$className}";
        }

        if (class_exists($fqcn)) {
            $handler = app()->make($fqcn);
            if ($handler instanceof CommandInterface) {
                return $handler;
            }
        }

        if (! class_exists($fqcn)) {
            DiscordLogHelper::error('Command handler class does not exist', [
                'class' => $fqcn,
            ]);
        }

        return null;
    }

    public function stateLoader(string $state): ?StateHandleInterface
    {
        $className = Str::studly($state, '_').'Handle';
        $fqcn = 'Modules\\'.$this->getModuleName()."\\TelegramStateHandles\\{$className}";

        if (! class_exists($fqcn)) {
            $fqcn = "Modules\\TelegramBot\\TelegramStateHandles\\{$className}";
        }

        if (class_exists($fqcn)) {
            $handler = app()->make($fqcn);
            if ($handler instanceof StateHandleInterface) {
                return $handler;
            }
        }

        if (! class_exists($fqcn)) {
            DiscordLogHelper::error('State handler class does not exist', [
                'class' => $fqcn,
            ]);
        }

        return null;
    }

    public static function migrateTenant(TelegramBot $bot): void
    {
        $bot->makeCurrent();
        $migrationPaths = [
            'database/migrations',
            'Modules/TelegramBot/database/migrations',
            'Modules/TelegramStarPayment/database/migrations',
            'Modules/TelegramTonPayment/database/migrations',
            'Modules/TimeLockedWallet/database/migrations',
            'Modules/TelegramIstar/database/migrations',
        ];
        foreach ($migrationPaths as $path) {
            Artisan::call('migrate', [
                '--database' => 'tenant',
                '--path' => $path,
                '--force' => true,
            ]);
        }
    }
}
