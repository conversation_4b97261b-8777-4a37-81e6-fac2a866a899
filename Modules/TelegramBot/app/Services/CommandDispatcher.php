<?php

namespace Modules\TelegramBot\Services;

use App\Helpers\DiscordLogHelper;
use Illuminate\Support\Str;
use Modules\TelegramBot\Models\TelegramUser;

class CommandDispatcher
{
    public function __construct() {}

    /**
     * Dispatch a command to its corresponding handler
     *
     * @param  string  $command  The command name (without the /)
     * @param  array  $message  The Telegram message data
     * @param  TelegramUser  $user  The user who sent the command
     * @return array Response array with success status and handled flag
     */
    public function dispatch(string $command, array $message, TelegramUser $user): array
    {
        $service = new TelegramBotService();

        $user->last_command = Str::lower($command);
        $user->save();

        try {
            $handler = $service->commandLoader($command);
            if (! $handler) {
                DiscordLogHelper::error('No handler found for command', [
                    'command' => $command,
                    'user_id' => $user->tele_id,
                    'message' => $message,
                ]);

                return ['success' => false, 'handled' => false];
            }

            $params = $service->parseCommand($message['text'] ?? '');

            return $handler->handle($message['chat']['id'], $user, $params);

        } catch (\Exception $e) {
            DiscordLogHelper::error('Error dispatching command', [
                'command' => $command,
                'user_id' => $user->tele_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ['success' => false, 'handled' => false];
        }
    }
}
