<?php

namespace Modules\TelegramBot\Services;

use App\Helpers\DiscordLogHelper;
use Illuminate\Support\Str;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCheckouts\CheckoutCallbackInterface;

class CheckoutCallbackDispatcher
{
    public function __construct() {}

    /**
     * Dispatch a checkout callback to its corresponding handler
     *
     * @param  string  $checkoutType  The checkout type identifier
     * @param  array  $callbackData  The Telegram callback data
     * @param  TelegramUser  $user  The user who initiated the checkout
     * @return array Response array with success status and handled flag
     */
    public function dispatch(string $checkoutType, array $callbackData, TelegramUser $user): array
    {
        try {
            $handler = $this->dynamicLoadHandler($checkoutType);
            if (! $handler) {
                DiscordLogHelper::error('No handler found for checkout callback', [
                    'checkout_type' => $checkoutType,
                    'user_id' => $user->tele_id,
                    'callback_data' => $callbackData,
                ]);

                return ['success' => false, 'handled' => false];
            }

            return $handler->handle($callbackData, $user, $callbackData['message']['chat']['id'] ?? null);

        } catch (\Exception $e) {
            DiscordLogHelper::error('Error dispatching checkout callback', [
                'checkout_type' => $checkoutType,
                'user_id' => $user->tele_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ['success' => false, 'handled' => false];
        }
    }

    private function getModuleName(): string
    {
        $tenant = app('currentTenant');
        $settings = json_decode($tenant->settings, true);

        return $settings['module'] === 'default' ? 'TelegramBot' : $settings['module'];
    }

    private function dynamicLoadHandler(string $checkoutType): ?CheckoutCallbackInterface
    {
        $className = Str::studly($checkoutType).'Checkout';
        $fqcn = 'Modules\\'.$this->getModuleName()."\\TelegramCheckouts\\{$className}";

        if (! class_exists($fqcn)) {
            // Fallback to the default module if the checkout class doesn't exist in the current module
            $fqcn = "Modules\\TelegramBot\\TelegramCheckouts\\{$className}";
        }

        if (class_exists($fqcn)) {
            $handler = app()->make($fqcn);
            if ($handler instanceof CheckoutCallbackInterface) {
                return $handler;
            }
        }

        if (! class_exists($fqcn)) {
            DiscordLogHelper::error('Checkout callback handler class does not exist', [
                'class' => $fqcn,
            ]);
        }

        return null;
    }
}
