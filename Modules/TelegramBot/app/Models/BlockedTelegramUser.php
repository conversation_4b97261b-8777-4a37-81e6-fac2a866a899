<?php

namespace Modules\TelegramBot\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlockedTelegramUser extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'blocked_telegram_users';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'reason',
    ];

    /**
     * Get the telegram user that is blocked.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'user_id');
    }
}
