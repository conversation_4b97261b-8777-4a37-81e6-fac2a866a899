<?php

namespace Modules\TelegramBot\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TelegramWebhookLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'update_id',
        'update_type',
        'payload',
        'status',
        'error_message',
        'processed_at',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'update_id' => 'integer',
        'payload' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Mark webhook as processed successfully
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'status' => 'processed',
            'processed_at' => now(),
            'error_message' => null,
        ]);
    }

    /**
     * Mark webhook as failed with error
     */
    public function markAsFailed(string $error): void
    {
        $this->update([
            'status' => 'failed',
            'processed_at' => now(),
            'error_message' => $error,
        ]);
    }

    /**
     * Mark webhook as pending
     */
    public function markAsPending(): void
    {
        $this->update([
            'status' => 'pending',
            'processed_at' => null,
            'error_message' => null,
        ]);
    }

    /**
     * Check if webhook was processed successfully
     */
    public function isProcessed(): bool
    {
        return $this->status === 'processed';
    }

    /**
     * Check if webhook failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if webhook is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Get the update data from payload
     */
    public function getUpdateData(): ?array
    {
        return $this->payload;
    }

    /**
     * Get the Telegram user ID from the update
     */
    public function getTelegramUserId(): ?int
    {
        $payload = $this->payload;

        if (isset($payload['message']['from']['id'])) {
            return $payload['message']['from']['id'];
        }

        if (isset($payload['callback_query']['from']['id'])) {
            return $payload['callback_query']['from']['id'];
        }

        if (isset($payload['inline_query']['from']['id'])) {
            return $payload['inline_query']['from']['id'];
        }

        return null;
    }

    /**
     * Get the chat ID from the update
     */
    public function getChatId(): ?int
    {
        $payload = $this->payload;

        if (isset($payload['message']['chat']['id'])) {
            return $payload['message']['chat']['id'];
        }

        if (isset($payload['callback_query']['message']['chat']['id'])) {
            return $payload['callback_query']['message']['chat']['id'];
        }

        return null;
    }

    /**
     * Scope to get only processed webhooks
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * Scope to get only failed webhooks
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get only pending webhooks
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get webhooks by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('update_type', $type);
    }

    /**
     * Scope to get recent webhooks
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }
}
