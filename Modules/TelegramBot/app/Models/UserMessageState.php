<?php

namespace Modules\TelegramBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\TelegramBot\Database\Factories\UserMessageStateFactory;

class UserMessageState extends Model
{
    use HasFactory;

    protected $table = 'user_message_state';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'telegram_user_id',
        'state',
        'data',
    ];
}
