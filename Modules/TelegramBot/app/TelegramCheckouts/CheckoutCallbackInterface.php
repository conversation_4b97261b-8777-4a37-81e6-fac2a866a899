<?php

namespace Modules\TelegramBot\TelegramCheckouts;

use Mo<PERSON><PERSON>\TelegramBot\Models\TelegramUser;

interface CheckoutCallbackInterface
{
    /**
     * Handle the checkout callback
     *
     * @param  array  $callbackData  The callback data from Telegram
     * @param  TelegramUser  $user  The user who initiated the checkout
     * @param  int|string $chatId  The chat ID
     * @return array Response array with success status and handled flag
     */
    public function handle(array $callbackData, TelegramUser $user, int|string|null $chatId): array;
}
