<?php

namespace Modules\TelegramBot\TelegramCheckouts;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;

class AddStarCheckout implements CheckoutCallbackInterface
{
    public function handle(array $callbackData, TelegramUser $user, int|string|null $chatId): array
    {
        if (! isset($callbackData['amount'])) {
            DiscordLogHelper::error('AddStarCheckout: Missing amount in callback data', [
                'user_id' => $user->tele_id,
                'callback_data' => $callbackData,
            ]);

            return [
                'success' => false,
                'handled' => false,
            ];
        }

        try {
            $amount = $callbackData['amount'];

            $user->addBalance($amount, 'STAR', 'Add funds via checkout');
            $user->save();

            if ($chatId) {
                $client = new \Modules\TelegramClient\Services\TelegramBotClient();
                $client->sendMessage($chatId, "Add funds success. Your new balance is {$user->balance_star} STAR.");
            }

            return [
                'success' => true,
                'handled' => true,
            ];

        } catch (\Exception $e) {
            DiscordLogHelper::error('Error handling AddStarCheckout callback', [
                'user_id' => $user->tele_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'handled' => false,
            ];
        }
    }
}
