<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramBot;
use Modules\TelegramBot\Services\TelegramBotService;

class MigrateTenant extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migrate:tenant';

    /**
     * The console command description.
     */
    protected $description = 'Migrate tenant databases.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle() {

        $this->info('Migrating tenant databases...');

        $tenants = TelegramBot::all();
        foreach ($tenants as $tenant) {
            TelegramBotService::migrateTenant($tenant);
        }

        $this->info('Tenant database migration completed.');
    }
}
