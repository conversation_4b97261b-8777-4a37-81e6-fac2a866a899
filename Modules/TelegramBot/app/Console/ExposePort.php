<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class ExposePort extends Command
{
    protected $signature = 'tunnel:expose {--port=} {--subdomain=}';

    protected $description = 'Expose local port to internet using cloudflared tunnel';

    private $process;

    public function handle()
    {
        $port = $this->option('port') ?: 8000;
        $subdomain = $this->option('subdomain');

        $this->info("Starting cloudflared tunnel for port: {$port}");

        // Kiểm tra xem cloudflared có sẵn không
        if (! $this->checkCloudflaredExists()) {
            $this->error('cloudflared not found. Please install it first.');

            return 1;
        }

        // Tạo tunnel URL
        $tunnelUrl = $this->createTunnel($port, $subdomain);

        if ($tunnelUrl) {
            $this->info('Tunnel created successfully!');
            $this->info("Your application is now accessible at: {$tunnelUrl}");

            // Update APP_URL in .env file
            $this->updateAppUrl($tunnelUrl);
            $this->info('APP_URL updated in .env file');
            $this->info('Press Ctrl+C to stop the tunnel');

            // Đợi cho đến khi process bị dừng
            $this->waitForStop();
        }

        return 0;
    }

    private function checkCloudflaredExists(): bool
    {
        $process = new Process(['which', 'cloudflared']);
        $process->run();

        return $process->isSuccessful();
    }

    private function createTunnel(string $port, ?string $subdomain = null): ?string
    {
        $command = ['cloudflared', 'tunnel', '--url', "localhost:{$port}"];

        // Thêm subdomain nếu có
        if ($subdomain) {
            $command[] = '--subdomain';
            $command[] = $subdomain;
        }

        $this->process = new Process($command);
        $this->process->setTimeout(null);

        // Bắt đầu process
        $this->process->start();

        $tunnelUrl = null;
        $attempts = 0;
        $maxAttempts = 30;

        // Đợi để lấy tunnel URL
        while ($attempts < $maxAttempts && $this->process->isRunning()) {
            $output = $this->process->getIncrementalOutput();
            $errorOutput = $this->process->getIncrementalErrorOutput();

            // Tìm URL trong output
            if (preg_match('/https:\/\/[^\s]+\.trycloudflare\.com/', $output.$errorOutput, $matches)) {
                $tunnelUrl = $matches[0];
                break;
            }

            sleep(1);
            $attempts++;
        }

        if (! $tunnelUrl && $attempts >= $maxAttempts) {
            $this->error('Failed to get tunnel URL within timeout period');
            $this->process->stop();

            return null;
        }

        return $tunnelUrl;
    }

    private function updateAppUrl(string $url): void
    {
        $envPath = base_path('.env');

        if (! file_exists($envPath)) {
            $this->warn('.env file not found, skipping APP_URL update');

            return;
        }

        $envContent = file_get_contents($envPath);

        // Update APP_URL if it exists, or add it if it doesn't
        if (preg_match('/^APP_URL=.*$/m', $envContent)) {
            $envContent = preg_replace('/^APP_URL=.*$/m', "APP_URL={$url}", $envContent);
        } else {
            $envContent .= "\nAPP_URL={$url}";
        }

        file_put_contents($envPath, $envContent);
    }

    private function waitForStop(): void
    {
        // Đăng ký signal handler để dừng process khi nhận Ctrl+C
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGINT, function () {
                $this->info("\nStopping tunnel...");
                if ($this->process && $this->process->isRunning()) {
                    $this->process->stop();
                }
                exit(0);
            });
        }

        // Đợi cho đến khi process dừng
        while ($this->process && $this->process->isRunning()) {
            $output = $this->process->getIncrementalOutput();
            $errorOutput = $this->process->getIncrementalErrorOutput();

            if (! empty($output)) {
                $this->line($output);
            }

            if (! empty($errorOutput)) {
                $this->error($errorOutput);
            }

            // Xử lý signal nếu có
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }

            usleep(100000); // 0.1 second
        }
    }
}
