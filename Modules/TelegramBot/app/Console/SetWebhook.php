<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Modules\TelegramBot\Models\TelegramBot;

class SetWebhook extends Command
{
    protected $signature = 'telegram:set-webhook';

    public function handle()
    {
        $bots = TelegramBot::all();
        foreach ($bots as $bot) {
            $bot->setWebhookUrl();
            sleep(1);
        }
        $this->info('Webhook set successfully');
    }
}
