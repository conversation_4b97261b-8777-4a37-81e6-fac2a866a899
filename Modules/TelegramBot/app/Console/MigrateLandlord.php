<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class MigrateLandlord extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migrate:landlord';

    /**
     * The console command description.
     */
    protected $description = 'Migrate landlord database';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $paths = [
            'Modules/TelegramBot/database/migrations/landlord',
            'database/migrations/landlord',
        ];

        $this->info('Migrating...');
        Artisan::call('migrate', [
            '--database' => 'landlord',
            '--path' => $paths,
            '--force' => true,
        ]);
        $this->info('Migrated successfully');
    }
}
