<?php

namespace Mo<PERSON>les\TelegramBot\Console;

use Illuminate\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramBot;
use Modules\TelegramBot\Services\TelegramBotService;

class InitBot extends Command
{
    protected $signature = 'telegram:init-bot';

    public function handle()
    {
        $this->info('Initializing bot...');
        $bots = [
            [
                'token' => '7053594957:AAHQQoNZYEZrvTtW8Uz20oYemmdta0Rh3Vw',
                'module' => 'TelegramIstar',
            ],
        ];

        foreach ($bots as $bot) {
            $exists = TelegramBot::where('token', $bot['token'])->first();
            if ($exists) {
                continue;
            }

            (new TelegramBotService)->createBotFromToken($bot['token'], $bot['module']);
            sleep(1);
            $this->info("Bot with token {$bot['token']} initialized.");
        }
    }
}
