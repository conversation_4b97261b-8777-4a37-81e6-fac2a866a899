<?php

namespace Modules\TelegramBot\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TelegramPreCheckoutReceived
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param  array  $webhookData  The complete webhook data from Telegram
     */
    public function __construct(public array $webhookData)
    {
        //
    }
}
