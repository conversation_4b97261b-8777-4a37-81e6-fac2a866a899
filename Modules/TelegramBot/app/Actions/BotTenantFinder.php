<?php

namespace Modules\TelegramBot\Actions;

use Illuminate\Http\Request;
use Modules\TelegramBot\Models\TelegramBot;
use Spatie\Multitenancy\Contracts\IsTenant;
use Spatie\Multitenancy\TenantFinder\TenantFinder;

class BotTenantFinder extends TenantFinder
{
    public function findForRequest(Request $request): ?IsTenant
    {
        $headerToken = $request->header('X-Telegram-Bot-Api-Secret-Token');

        if (! $headerToken) {
            return null;
        }

        $telegramBot = TelegramBot::where('secret', $headerToken)->first();

        if (! $telegramBot) {
            throw new \Exception('Telegram bot not found');
        }

        return $telegramBot;
    }
}
