<?php

namespace Modules\TelegramBot\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Middlewares\AdminMiddleware;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Models\BlockedTelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;

class BlockCommand implements CommandInterface
{
    public function __construct() {}

    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Handling /block command for user '.$user->tele_id);

            if (empty($params) || (!isset($params['username']) && !isset($params['tele_id']))) {
                DiscordLogHelper::log('Invalid /block command format');

                $botClient->sendMessage($chatId, 'Usage: '.$this->help());

                return ['success' => true, 'handled' => true];
            }

            $username = $params['username'] ?? null;
            $teleId = $params['tele_id'] ?? null;
            $reason = $params['reason'] ?? '';

            // Find the telegram user to block
            $targetUser = null;

            if ($username) {
                // Remove @ symbol if present
                $username = ltrim($username, '@');
                $targetUser = TelegramUser::where('username', $username)->first();
                $searchParam = "@{$username}";
            } elseif ($teleId) {
                $targetUser = TelegramUser::where('tele_id', $teleId)->first();
                $searchParam = "ID {$teleId}";
            }

            if (!$targetUser) {
                $botClient->sendMessage($chatId, "❌ Telegram user with {$searchParam} not found.");
                return ['success' => true, 'handled' => true];
            }

            if ($targetUser->isBlocked()) {
                $identifier = $targetUser->username ? "@{$targetUser->username}" : "ID {$targetUser->tele_id}";
                $botClient->sendMessage($chatId, "⚠️ Telegram user {$identifier} is already blocked.");
                return ['success' => true, 'handled' => true];
            }

            try {
                // Create block record in database
                BlockedTelegramUser::create([
                    'user_id' => $targetUser->id,
                    'blocked_by_user_id' => $user->id,
                    'reason' => $reason,
                ]);

                $identifier = $targetUser->username ? "@{$targetUser->username}" : "ID {$targetUser->tele_id}";

                DiscordLogHelper::log("BLOCK ACTION: Telegram user {$identifier} ({$targetUser->name}) blocked by admin {$user->tele_id} for reason: {$reason}");

                $message = "✅ Telegram user {$targetUser->name} ({$identifier}) has been blocked successfully.";
                if ($reason !== 'No reason provided') {
                    $message .= "\nReason: {$reason}";
                }

                $botClient->sendMessage($chatId, $message);

                return ['success' => true, 'handled' => true];

            } catch (\Exception $e) {
                DiscordLogHelper::log("Failed to block telegram user {$searchParam}: " . $e->getMessage());

                $botClient->sendMessage($chatId, "❌ Failed to process block command. Please try again later.");

                return ['success' => true, 'handled' => true];
            }
        });
    }

    protected function help(): string
    {
        return '/block username=<@username> [reason=<reason>] OR /block tele_id=<telegram_user_id> [reason=<reason>]';
    }
}
