<?php

namespace Modules\TelegramBot\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Enums\BotService;
use Modules\TelegramBot\Middlewares\AdminMiddleware;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\TelegramBotService;
use Modules\TelegramClient\Services\TelegramBotClient;

class AddbotCommand implements CommandInterface
{
    public function __construct() {}

    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $botClient = new TelegramBotClient;
            $service = new TelegramBotService;

            DiscordLogHelper::log('Handling /addbot command for user '.$user->tele_id);

            if (empty($params) || ! isset($params['token'])) {
                DiscordLogHelper::log('Invalid /addbot command format');

                $botClient->sendMessage($chatId, 'Usage: '.$this->help());

                return ['success' => true, 'handled' => true];
            }

            $botToken = $params['token'] ?? null;
            $module = $params['module'] ?? 'default';

            if ($module !== 'default' && ! BotService::isValidModule($module)) {
                $botClient->sendMessage($chatId, 'Invalid module name. '.$this->help());

                return ['success' => true, 'handled' => true];
            }

            if (empty($botToken)) {
                $botClient->sendMessage($chatId, 'Invalid bot token. '.$this->help());

                return ['success' => true, 'handled' => true];
            }

            $exists = \Modules\TelegramBot\Models\TelegramBot::where('token', $botToken)->first();
            if ($exists) {
                $botClient->sendMessage($chatId, 'Bot with this token already exists.');

                return ['success' => true, 'handled' => true];
            }

            $service->createBotFromToken($botToken, $module);

            $botClient->sendMessage($chatId, 'Bot added successfully.');

            return ['success' => true, 'handled' => true];
        });
    }

    protected function help()
    {
        $modules = BotService::getAvailableModules();
        $modulesList = implode(', ', $modules);

        return '/addbot token=<bot_token> module=<module_name> (available modules: '.$modulesList.')';
    }
}
