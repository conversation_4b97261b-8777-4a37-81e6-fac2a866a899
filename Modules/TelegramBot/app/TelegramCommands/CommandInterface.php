<?php

namespace Modules\TelegramBot\TelegramCommands;

use Mo<PERSON>les\TelegramBot\Models\TelegramUser;

interface CommandInterface
{
    /**
     * Handle the command execution
     *
     * @param  string|int  $chatId  The chat ID where the command was issued
     * @param  array  $params  The command parameters
     * @param  TelegramUser  $user  The user who sent the command
     * @return array Response array with success status and handled flag
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array;
}
