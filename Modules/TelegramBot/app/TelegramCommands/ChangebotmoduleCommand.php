<?php

namespace Modules\TelegramBot\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Enums\BotService;
use Modules\TelegramBot\Models\TelegramBot;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\TelegramBotService;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Middlewares\AdminMiddleware;

class ChangebotmoduleCommand implements CommandInterface
{
    public function __construct() {}

    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $botClient = new TelegramBotClient;
            $service = new TelegramBotService;

            DiscordLogHelper::log('Handling /changebotmodule command for user '.$user->tele_id);

            if (empty($params) || (! isset($params['bot_id']) && ! isset($params['token'])) || ! isset($params['module'])) {
                DiscordLogHelper::log('Invalid /changebotmodule command format');

                $botClient->sendMessage($chatId, 'Usage: '.$this->help());

                return ['success' => true, 'handled' => true];
            }

            $botId = $params['bot_id'] ?? null;
            $botToken = $params['token'] ?? null;
            $newModule = $params['module'];

            // Validate module
            if ($newModule !== 'default' && ! BotService::isValidModule($newModule)) {
                $botClient->sendMessage($chatId, 'Invalid module name. '.$this->help());

                return ['success' => true, 'handled' => true];
            }

            // Find bot by ID or token
            $bot = null;
            if ($botId) {
                $bot = TelegramBot::find($botId);
                if (! $bot) {
                    $botClient->sendMessage($chatId, "Bot with ID '$botId' not found.");

                    return ['success' => true, 'handled' => true];
                }
            } elseif ($botToken) {
                $bot = TelegramBot::where('token', $botToken)->first();
                if (! $bot) {
                    $botClient->sendMessage($chatId, "Bot with token '$botToken' not found.");

                    return ['success' => true, 'handled' => true];
                }
            }

            // Get current module before update
            $currentSettings = json_decode($bot->settings, true) ?? [];
            $oldModule = $currentSettings['module'] ?? 'default';

            // Update module using service method
            $service->updateBotModule($bot, $newModule);

            DiscordLogHelper::log("Changed bot module for bot '{$bot->name}' from '$oldModule' to '$newModule'");

            $botClient->sendMessage(
                $chatId,
                "Bot module changed successfully.\nBot: {$bot->name}\nOld Module: $oldModule\nNew Module: $newModule"
            );

            return ['success' => true, 'handled' => true];
        });
    }

    protected function help(): string
    {
        $modules = array_merge(['default'], BotService::getAvailableModules());
        $modulesList = implode(', ', $modules);

        return '/changebotmodule bot_id=<bot_id> module=<module_name> OR /changebotmodule token=<bot_token> module=<module_name> (available modules: '.$modulesList.')';
    }
}
