<?php

namespace Modules\TelegramBot\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Enums\CheckoutTypeEnum;

class AddfundCommand implements CommandInterface
{
    public function __construct() {}

    /**
     * Handle the /addfund command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $botClient = new TelegramBotClient;

        DiscordLogHelper::log('AddfundCommand: '.DiscordLogHelper::formatJson($params));

        if (empty($params) || ! isset($params['amount']) || ! $this->validateAmount($params['amount'])) {
            DiscordLogHelper::log('Invalid /addfund command format');

            $botClient->sendMessage($chatId, $this->help());

            return ['success' => true, 'handled' => true];
        }

        $amount = $params['amount'] ?? null;

        $amount = (int) $amount;

        try {
            $invoiceData = [
                'title' => 'Add Funds to Bot',
                'description' => "Add {$amount} Telegram Stars to your bot balance",
                'payload' => json_encode([
                    'type' => CheckoutTypeEnum::ADD_STAR->value,
                    'user_id' => $user->tele_id,
                    'amount' => $amount,
                    'timestamp' => time(),
                ]),
                'prices' => [
                    ['label' => 'Telegram Stars', 'amount' => $amount],
                ],
            ];

            DiscordLogHelper::log('Creating invoice for user '.$user->tele_id.' with amount: '.$amount);

            $invoiceResult = $botClient->createInvoiceLink($invoiceData);

            if (! $invoiceResult['success']) {
                DiscordLogHelper::error('Failed to create invoice', [
                    'user_id' => $user->tele_id,
                    'amount' => $amount,
                    'error' => $invoiceResult['message'] ?? 'Unknown error',
                ]);

                $errorMessage = "Sorry, we couldn't create the payment link at this moment. Please try again later.";
                $botClient->sendMessage($chatId, $errorMessage);

                return ['success' => true, 'handled' => true];
            }

            $invoiceLink = $invoiceResult['invoice_link'];

            $successMessage = "💰 <b>Add Funds</b>\n\n";
            $successMessage .= "Amount: {$amount} Telegram Stars ⭐\n\n";
            $successMessage .= 'Click the button below to proceed with the payment:';

            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => "Pay {$amount} ⭐", 'url' => $invoiceLink],
                    ],
                ],
            ];

            $botClient->sendMessage(
                $chatId,
                $successMessage,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'HTML',
                ]
            );

            DiscordLogHelper::log('Invoice link sent successfully to user '.$user->tele_id);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('Exception in AddfundCommand', [
                'user_id' => $user->tele_id,
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $errorMessage = 'An error occurred while processing your request. Please try again later.';
            $botClient->sendMessage($chatId, $errorMessage);

            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Validate the amount parameter
     */
    private function validateAmount($amount): bool
    {
        if (! is_numeric($amount)) {
            return false;
        }

        $amount = (float) $amount;

        if ($amount <= 0) {
            return false;
        }

        if ($amount != (int) $amount) {
            return false;
        }

        return true;
    }

    private function help(): string
    {
        $helpMessage = "Usage: /addfund amount=<stars_amount>\n\n";
        $helpMessage .= "Example: /addfund amount=100\n";
        $helpMessage .= 'This will create an invoice for 100 Telegram Stars.';

        return $helpMessage;
    }
}
