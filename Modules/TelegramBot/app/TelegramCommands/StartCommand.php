<?php

namespace Modules\TelegramBot\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class StartCommand implements CommandInterface
{
    public function __construct() {}

    /**
     * Handle the /start command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $messageStateService = new MessageStateService;
        $messageStateService->clearUserState($user);

        $botClient = new TelegramBotClient;

        DiscordLogHelper::log('StartCommand: '.DiscordLogHelper::formatJson($params));
        $welcomeMessage = "Hello, Mate! 🤝\n\n";
        $welcomeMessage .= "🚀 Welcome to NiTon, the groundbreaking app built on Telegram!\n\n";
        $welcomeMessage .= "Explore endless possibilities with cloud mining for TON. Our platform, powered by the TON blockchain, ensures optimized transactions and lower transfer fees.\n\n";
        $welcomeMessage .= "Complete missions, invite friends, and rent additional mining power to boost your earnings even more.\n\n";
        $welcomeMessage .= 'Tap to Play 👇';

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => 'Play & Earn 🎮', 'web_app' => ['url' => config('telegrambot.ui.mini_app_url')]],
                ],
                [
                    ['text' => 'Official Channel ✅', 'url' => config('telegrambot.ui.channel_url')],
                ],
                [
                    ['text' => 'How to Boost Miner?', 'url' => config('telegrambot.ui.how_to_boost_url')],
                ],
                [
                    ['text' => 'venuspay app', 'url' => 'https://t.me/Venuspaybot'],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $welcomeMessage,
            [
                'reply_markup' => json_encode($keyboard),
            ]
        );

        return ['success' => true, 'handled' => true];
    }
}
