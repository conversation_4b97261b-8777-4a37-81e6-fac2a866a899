<?php

namespace Modules\TelegramBot\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramBot;
use Modules\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Middlewares\AdminMiddleware;

class ListbotsCommand implements CommandInterface
{
    public function __construct() {}

    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user) {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Handling /listbot command for user '.$user->tele_id);

            $bots = TelegramBot::orderBy('created_at', 'desc')->get();

            if ($bots->isEmpty()) {
                $botClient->sendMessage($chatId, 'No bots found.');

                return ['success' => true, 'handled' => true];
            }

            $botsList = "📋 **Bot List:**\n\n";
            foreach ($bots as $bot) {
                $settings = json_decode($bot->settings, true) ?? [];
                $module = $settings['module'] ?? 'default';

                $botsList .= "🤖 **{$bot->name}**\n";
                $botsList .= "   • ID: {$bot->id}\n";
                $botsList .= "   • Module: {$module}\n";
                $botsList .= '   • Created: '.$bot->created_at->format('Y-m-d H:i')."\n\n";
            }

            $botClient->sendMessage($chatId, $botsList);

            return ['success' => true, 'handled' => true];
        });
    }
}
