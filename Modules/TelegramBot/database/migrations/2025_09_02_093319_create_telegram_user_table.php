<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telegram_user', function (Blueprint $table) {
            $table->id();
            $table->string('tele_id')->unique();
            $table->string('name');
            $table->string('username')->nullable();
            $table->string('language_code')->nullable();
            $table->string('avatar_url')->nullable();
            $table->timestamp('last_active')->nullable();
            $table->decimal('balance_ton', 20, 8)->default(0);
            $table->decimal('balance_star', 20, 8)->default(0);
            $table->string('referral_code', 8)->nullable()->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telegram_user');
    }
};
