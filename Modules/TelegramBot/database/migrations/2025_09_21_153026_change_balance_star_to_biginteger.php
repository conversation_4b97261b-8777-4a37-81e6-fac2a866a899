<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('telegram_user', function (Blueprint $table) {
            $table->bigInteger('balance_star')->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('telegram_user', function (Blueprint $table) {
            // Change back to decimal
            $table->decimal('balance_star', 20, 8)->default(0)->change();

            // Convert bigInteger values back to decimal by dividing by 10^8
            DB::statement('UPDATE telegram_user SET balance_star = balance_star / 100000000.0');
        });
    }
};
