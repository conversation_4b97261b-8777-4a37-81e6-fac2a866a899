<?php

use Illuminate\Support\Facades\Route;
use Modules\TelegramBot\Http\Controllers\TelegramWebhookController;

/*
|--------------------------------------------------------------------------
| Telegram Bot API Routes
|--------------------------------------------------------------------------
*/

// Public webhook endpoint (no authentication required)
Route::prefix('telegram-bot')->group(function () {
    Route::match(['get', 'post'], '/webhook', [TelegramWebhookController::class, 'handleWebhook']);
});

// Admin routes (authentication required)
Route::middleware(['admin.auth'])->prefix('admin/telegram-bot')->group(function () {
    // Webhook management
    Route::get('/webhook/info', [TelegramWebhookController::class, 'getWebhookInfo']);
    Route::post('/webhook/set', [TelegramWebhookController::class, 'setWebhook']);
    Route::post('/webhook/remove', [TelegramWebhookController::class, 'removeWebhook']);
    Route::get('/webhook/logs', [TelegramWebhookController::class, 'getWebhookLogs']);
});

// Test route (no authentication required)
Route::get('/telegram-bot/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'TelegramBot module is working!',
        'module_name' => 'TelegramBot',
        'timestamp' => now()->toISOString(),
        'config' => [
            'bot_configured' => ! empty(config('telegrambot.bot.token')),
            'webhook_configured' => ! empty(config('telegrambot.webhook.url')),
            'logging_enabled' => config('telegrambot.logging.webhook_requests', false),
        ],
        'database_tables' => [
            'users_table_ready' => \Illuminate\Support\Facades\Schema::hasColumn('users', 'tele_id'),
            'telegram_webhook_logs' => \Illuminate\Support\Facades\Schema::hasTable('telegram_webhook_logs'),
        ],
    ]);
});
