<?php

return [
    'name' => 'TelegramBot',

    /*
    |--------------------------------------------------------------------------
    | Telegram Bot Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the TelegramBot module. This extends the main
    | telegram configuration with module-specific settings.
    |
    */

    // Bot configuration
    'bot' => [
        'token' => env('TELEGRAM_BOT_TOKEN'),
        'username' => env('TELEGRAM_BOT_USERNAME'),
        'name' => env('TELEGRAM_BOT_NAME', 'Mining Bot'),
    ],

    // Message handling
    'messages' => [
        'welcome' => [
            'title' => '🚀 Welcome to NiTon Mining Bot!',
            'subtitle' => '⛏️ Your Gateway to Cloud Mining Excellence',
            'description' => 'Explore endless possibilities with cloud mining for TON. Our platform, powered by the TON blockchain, ensures optimized transactions and lower transfer fees.',
            'features' => [
                '💎 Complete missions and earn rewards',
                '👥 Invite friends to boost your earnings',
                '⚡ Rent additional mining power',
                '📊 Real-time mining statistics',
                '🔒 Secure and transparent operations',
            ],
            'cta' => '🎮 Tap to Play and start your mining journey!',
            'footer' => '💬 Need help? Use /help for available commands',
        ],
        'help' => "Available commands:\n/start - Start the bot\n/help - Show this help message\n/status - Check your mining status",
        'unknown_command' => 'Sorry, I don\'t understand that command. Type /help for available commands.',
        'error' => 'An error occurred while processing your request. Please try again later.',
    ],

    // Commands configuration
    'commands' => [
        'start' => [
            'description' => 'Start the bot',
            'enabled' => true,
        ],
        'help' => [
            'description' => 'Show help message',
            'enabled' => true,
        ],
        'status' => [
            'description' => 'Check mining status',
            'enabled' => true,
        ],
    ],

    'ui' => [
        'mini_app_url' => env('TELEGRAM_MINI_APP_URL'),
        'channel_url' => 'https://t.me/NiTon_News',
        'how_to_boost_url' => 'https://t.me/NiTon_News/72',
    ],
];
