<?php

namespace Modules\TimeLockedWallet\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * LedgerEntry Model
 *
 * Represents a ledger entry in the time-locked wallet system.
 * Each entry records either a credit or debit transaction with optional unlock time.
 *
 * @property int $id Primary key
 * @property int $account_id Foreign key to accounts table
 * @property string $type Transaction type (credit|debit)
 * @property string|null $subtype Transaction subtype (deposit|purchase|withdrawal|fee...)
 * @property string $amount Transaction amount (positive value)
 * @property string $currency Currency code (3 characters, e.g., 'USD')
 * @property \Carbon\Carbon $created_at Creation timestamp
 * @property \Carbon\Carbon|null $updated_at Update timestamp
 * @property \Carbon\Carbon|null $unlock_at Unlock timestamp (only for credit entries)
 * @property string|null $note Additional notes for the transaction
 */
class LedgerEntry extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ledger_entries';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'account_id',
        'type',
        'subtype',
        'amount',
        'currency',
        'note',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'unlock_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the account that owns the ledger entry.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function account(): BelongsTo
    {
        $configModel = config('TimeLockedWallet.account_model');

        if (!class_exists($configModel)) {
            throw new \RuntimeException("Account model class {$configModel} does not exist.");
        }

        return $this->belongsTo($configModel);
    }
}
