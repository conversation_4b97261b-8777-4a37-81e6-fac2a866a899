<?php

namespace Modules\TimeLockedWallet\Traits;

use Modules\TimeLockedWallet\Models\LedgerEntry;

trait UseLedgerEntry
{
    public function ledger()
    {
        return $this->hasMany(LedgerEntry::class, 'account_id', 'id');
    }

    public function addLedgerEntry(float $amount, string $type, ?string $currency, ?string $subtype = null, ?string $note = null): LedgerEntry
    {
        // Validate type
        if (!in_array($type, ['credit', 'debit'])) {
            throw new \InvalidArgumentException("Invalid ledger entry type: {$type}. Must be 'credit' or 'debit'.");
        }

        if ($amount <= 0) {
            throw new \InvalidArgumentException("Amount must be a positive value. Given: {$amount}");
        }

        $entry = new LedgerEntry([
            'type' => $type,
            'subtype' => $subtype ?? '',
            'amount' => $amount,
            'currency' => $currency ? strtoupper($currency) : config('timelockedwallet.default_currency', 'USD'),
            'note' => $note ?? '',
        ]);

        $this->ledger()->save($entry);

        return $entry;
    }

    public function getLockedDay() {   // can be overridden in model
        return config('timelockedwallet.locked_days', 21);
    }

    public function getBalance(): float // can be overridden in model
    {
        $field = config('timelockedwallet.balance_field');
        if (empty($field) || !property_exists($this, $field)) {
            throw new \RuntimeException("Balance field is not configured or does not exist on the model.");
        }

        return $this->{$field} ?? 0.0;
    }

    public function availableBalance(string $currency = 'STAR'): float
    {
        $creditsInlocked = $this->ledger()
            ->where('type', 'credit')
            ->where('currency', strtoupper($currency))
            ->where('created_at', '<=', now()->subDays($this->getLockedDay()))
            ->sum('amount');

        $availableForWithdrawal = $this->getBalance() - $creditsInlocked;

        return max(0, $availableForWithdrawal);
    }
}
