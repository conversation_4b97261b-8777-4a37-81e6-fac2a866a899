<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('ledger_entries', function (Blueprint $table) {
            $table->id(); // BIGSERIAL PRIMARY KEY

            $table->unsignedBigInteger('account_id');

            $model = config('timelockedwallet.account_model');
            if (!class_exists($model)) {
                throw new \RuntimeException("Account model class {$model} does not exist.");
            }

            $accountTable = config('timelockedwallet.account_table', (new $model)->getTable());
            $table->foreign('account_id')->references('id')->on($accountTable)->onDelete('cascade');

            $table->string('type');
            $table->string('subtype')->nullable();

            $table->decimal('amount', 18, 2);

            $table->string('currency');

            // Timestamps
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();

            // Additional notes
            $table->text('note')->nullable();

            $table->index(['account_id']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('ledger_entries');
    }
};
