<?php

namespace Modules\TelegramIstar\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Mo<PERSON>les\TelegramIstar\Helpers\IstarHelper;
use Modules\TelegramTonPayment\Events\TonTransactionVerified;
use Modules\TelegramBot\Models\TelegramUser;

class TonTransactionVerifiedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(TonTransactionVerified $event): void
    {
        $transaction = $event->transaction;

        $metaData = $transaction->meta_data ? json_decode($transaction->meta_data, true) : null;

        $user = TelegramUser::find($transaction->user_id);

        $service = new IstarHelper();

        if ($metaData && isset($metaData['type'])) {
            $service->processPurchase($user, $metaData);
        }
    }
}
