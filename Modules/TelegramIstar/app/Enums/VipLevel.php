<?php

namespace Modules\TelegramIstar\Enums;

class VipLevel
{
    /**
     * VIP level thresholds in stars
     */
    const LEVEL_0_THRESHOLD = 0;
    const LEVEL_1_THRESHOLD = 10000;
    const LEVEL_2_THRESHOLD = 100000;
    const LEVEL_3_THRESHOLD = 500000;
    const LEVEL_4_THRESHOLD = 1000000;

    /**
     * VIP level emojis
     */
    const LEVEL_0_EMOJI = '👤';
    const LEVEL_1_EMOJI = '⭐';
    const LEVEL_2_EMOJI = '🌟';
    const LEVEL_3_EMOJI = '💎';
    const LEVEL_4_EMOJI = '👑';

    /**
     * Get all VIP level thresholds
     *
     * @return array Array of level => threshold pairs
     */
    public static function getThresholds(): array
    {
        return [
            0 => self::LEVEL_0_THRESHOLD,
            1 => self::LEVEL_1_THRESHOLD,
            2 => self::LEVEL_2_THRESHOLD,
            3 => self::LEVEL_3_THRESHOLD,
            4 => self::LEVEL_4_THRESHOLD,
        ];
    }

    /**
     * Get all VIP level emojis
     *
     * @return array Array of level => emoji pairs
     */
    public static function getEmojis(): array
    {
        return [
            0 => self::LEVEL_0_EMOJI,
            1 => self::LEVEL_1_EMOJI,
            2 => self::LEVEL_2_EMOJI,
            3 => self::LEVEL_3_EMOJI,
            4 => self::LEVEL_4_EMOJI,
        ];
    }

    /**
     * Get VIP level based on star balance
     *
     * @param int $starBalance The user's star balance
     * @return int The VIP level (0-4)
     */
    public static function getLevelFromBalance(int $starBalance): int
    {
        if ($starBalance >= self::LEVEL_4_THRESHOLD) {
            return 4;
        } elseif ($starBalance >= self::LEVEL_3_THRESHOLD) {
            return 3;
        } elseif ($starBalance >= self::LEVEL_2_THRESHOLD) {
            return 2;
        } elseif ($starBalance >= self::LEVEL_1_THRESHOLD) {
            return 1;
        }

        return 0;
    }

    /**
     * Get the threshold for the next VIP level
     *
     * @param int $currentLevel The current VIP level
     * @return int|null The threshold for next level, or null if already at max level
     */
    public static function getNextLevelThreshold(int $currentLevel): ?int
    {
        $thresholds = self::getThresholds();
        $nextLevel = $currentLevel + 1;

        return $thresholds[$nextLevel] ?? null;
    }

    /**
     * Get emoji for a specific VIP level
     *
     * @param int $level The VIP level
     * @return string The emoji for the level
     */
    public static function getEmojiForLevel(int $level): string
    {
        $emojis = self::getEmojis();
        return $emojis[$level] ?? self::LEVEL_0_EMOJI;
    }

    /**
     * Calculate progress to next VIP level
     *
     * @param int $starBalance The user's current star balance
     * @param int $currentLevel The user's current VIP level
     * @return array Array with progress information
     */
    public static function getProgressToNextLevel(int $starBalance, int $currentLevel): array
    {
        $nextLevelThreshold = self::getNextLevelThreshold($currentLevel);
        
        if ($nextLevelThreshold === null) {
            return [
                'is_max_level' => true,
                'progress_percentage' => 100,
                'stars_needed' => 0,
                'current_level_threshold' => self::getThresholds()[$currentLevel],
            ];
        }

        $currentLevelThreshold = self::getThresholds()[$currentLevel];
        $starsInCurrentLevel = $starBalance - $currentLevelThreshold;
        $starsNeededForNextLevel = $nextLevelThreshold - $currentLevelThreshold;
        $progressPercentage = min(100, ($starsInCurrentLevel / $starsNeededForNextLevel) * 100);

        return [
            'is_max_level' => false,
            'progress_percentage' => round($progressPercentage, 1),
            'stars_needed' => $nextLevelThreshold - $starBalance,
            'current_level_threshold' => $currentLevelThreshold,
            'next_level_threshold' => $nextLevelThreshold,
            'stars_in_current_level' => $starsInCurrentLevel,
            'stars_needed_for_next_level' => $starsNeededForNextLevel,
        ];
    }
}
