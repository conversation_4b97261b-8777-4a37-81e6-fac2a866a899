<?php

namespace Modules\TelegramIstar\TelegramStateHandles;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Interfaces\StateHandleInterface;
use Modules\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;
use Modules\TelegramBot\Enums\CheckoutTypeEnum;

class TransferStarsHandle implements StateHandleInterface
{
    /**
     * Handle the transfer amount processing
     */
    public function handle(string $text, string|int $chatId, TelegramUser $user): void
    {
        try {
            $amount = (int) trim($text);

            if (! $amount) {
                $this->sendErrorMessage($chatId, 'Please provide a valid amount.');
                return;
            }

            // Validate amount
            if (! is_numeric($amount) || $amount < 5) {
                $this->sendInvalidAmountMessage($chatId);
                return;
            }

            DiscordLogHelper::log("Processing star transfer amount: {$amount} for user {$user->tele_id}");

            $this->createStarInvoice($chatId, $user, $amount);

        } catch (\Exception $e) {
            DiscordLogHelper::error('ProcessTransferAmountCommand failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $this->sendErrorMessage($chatId, 'An error occurred while processing your request.');
        }
    }

    /**
     * Create star invoice for the user
     */
    private function createStarInvoice(string|int $chatId, TelegramUser $user, int $amount): void
    {
        try {
            $botClient = new TelegramBotClient;

            // Create invoice data
            $invoiceData = [
                'title' => "Star Transfer - {$amount} ⭐",
                'description' => "Transfer {$amount} Telegram Stars to your bot balance",
                'payload' => json_encode([
                    'type' => CheckoutTypeEnum::ADD_STAR->value,
                    'amount' => $amount,
                    'user_id' => $user->tele_id,
                    'timestamp' => time(),
                ]),
                'prices' => [
                    [
                        'label' => 'Telegram Stars',
                        'amount' => $amount,
                    ],
                ],
            ];

            $result = $botClient->createInvoiceLink($invoiceData);

            if ($result['success']) {
                $message = "⭐ **Deposit Star**\n\n"
                    ."The amount to be paid is **{$amount}** your id: **{$user->tele_id}**. This amount will be credited to your balance and will be frozen for a period of several hours up to 21 days.\n\n"
                    ."After that, you will be able to withdraw it.\n"
                    ."The bot will notify you when the funds become available.\n\n"
                    .'Click the button below to complete the payment:';

                $keyboard = [
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '💳 Pay '.$amount.' ⭐',
                                'url' => $result['invoice_link'],
                            ],
                        ],
                    ],
                ];

                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'reply_markup' => json_encode($keyboard),
                        'parse_mode' => 'Markdown',
                    ]
                );

                // Clear user state
                $stateService = new MessageStateService();
                $stateService->clearUserState($user);
            } else {
                $this->sendErrorMessage($chatId, 'Failed to create payment invoice. Please try again.');
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to create star invoice: '.$e->getMessage());

            $this->sendErrorMessage($chatId, 'Failed to create payment invoice. Please try again.');
        }
    }

    /**
     * Send invalid amount message
     */
    private function sendInvalidAmountMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient;

        $message = "❌ **Invalid Amount**\n\n"
            ."Please enter a valid number of stars.\n"
            ."Minimum amount: **5 ⭐**\n"
            .'Example: 100';

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId, string $errorMessage): void
    {
        $botClient = new TelegramBotClient;

        $botClient->sendMessage(
            $chatId,
            '❌ '.$errorMessage,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
