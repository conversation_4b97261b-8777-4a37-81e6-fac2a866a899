<?php

namespace Modules\TelegramIstar\TelegramStateHandles;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Interfaces\StateHandleInterface;
use Modules\TelegramBot\Models\TelegramUser;
use <PERSON><PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class AddWalletHandle implements StateHandleInterface
{
    /**
     * Handle wallet address input from user
     *
     * @param  string  $text  The wallet address text entered by user
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user entering the wallet address
     * @return void
     */
    public function handle(string $text, string|int $chatId, TelegramUser $user): void
    {
        try {
            $walletAddress = trim($text);

            // Validate the wallet address format
            if (!$this->isValidTonAddress($walletAddress)) {
                $this->sendInvalidAddressMessage($chatId, $walletAddress);
                return;
            }

            // Check if user already has a wallet address
            $currentWallet = $user->wallet_address;
            if ($currentWallet && $currentWallet !== $walletAddress) {
                $this->sendUpdateConfirmation($chatId, $user, $currentWallet, $walletAddress);
                return;
            }

            // Save the wallet address
            $this->saveWalletAddress($user, $walletAddress);

            // Send success message
            $this->sendSuccessMessage($chatId, $walletAddress);

            // Clear user state
            $stateService = new MessageStateService();
            $stateService->clearUserState($user);

        } catch (\Exception $e) {
            DiscordLogHelper::error('AddWalletHandle failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId, 'An error occurred while saving your wallet address.');
        }
    }

    /**
     * Validate TON wallet address format
     *
     * @param  string  $address  The wallet address to validate
     * @return bool True if address is valid
     */
    private function isValidTonAddress(string $address): bool
    {
        // Remove any whitespace
        $address = trim($address);

        // Check raw format (0:hex_string)
        if (preg_match('/^-?[0-9]:[a-fA-F0-9]{64}$/', $address)) {
            return true;
        }

        // Check user-friendly format (UQ/EQ + 46 base64url characters)
        if (preg_match('/^[UE][Qq][A-Za-z0-9_-]{46}$/', $address)) {
            return true;
        }

        // Check other valid TON address formats
        if (preg_match('/^[kf][A-Za-z0-9_-]{46}$/', $address)) {
            return true;
        }

        return false;
    }

    /**
     * Save wallet address to user
     *
     * @param  TelegramUser  $user  The user to update
     * @param  string  $walletAddress  The wallet address to save
     * @return void
     */
    private function saveWalletAddress(TelegramUser $user, string $walletAddress): void
    {
        $user->update(['wallet_address' => $walletAddress]);
        DiscordLogHelper::log("Saved wallet address for user {$user->tele_id}: {$walletAddress}");
    }

    /**
     * Send invalid address message
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @param  string  $address  The invalid address that was entered
     * @return void
     */
    private function sendInvalidAddressMessage(string|int $chatId, string $address): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ **Invalid Wallet Address** ❌\n\n"
            . "The provided address is not a valid TON wallet address:\n"
            . "`{$address}`\n\n"
            . "**Valid TON address formats:**\n"
            . "• User-friendly: `UQAbc...` or `EQAbc...`\n"
            . "• Raw format: `0:1234abcd...`\n\n"
            . "**Please try again with a valid address.**\n\n"
            . "💡 *You can copy your address from your TON wallet app.*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔄 Try Again',
                        'callback_data' => 'add_wallet_prompt',
                    ],
                    [
                        'text' => '🔙 Cancel',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send update confirmation message
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @param  TelegramUser  $user  The user whose wallet is being updated
     * @param  string  $currentWallet  The current wallet address
     * @param  string  $newWallet  The new wallet address
     * @return void
     */
    private function sendUpdateConfirmation(string|int $chatId, TelegramUser $user, string $currentWallet, string $newWallet): void
    {
        $botClient = new TelegramBotClient();

        $shortCurrent = $this->shortenAddress($currentWallet);
        $shortNew = $this->shortenAddress($newWallet);

        $message = "🔄 **Update Wallet Address** 🔄\n\n"
            . "You already have a wallet address configured:\n"
            . "**Current:** `{$shortCurrent}`\n\n"
            . "**New:** `{$shortNew}`\n\n"
            . "Do you want to update your wallet address?";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '✅ Update',
                        'callback_data' => 'confirm_wallet_update_' . base64_encode($newWallet),
                    ],
                    [
                        'text' => '❌ Cancel',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send success message
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @param  string  $walletAddress  The wallet address that was saved
     * @return void
     */
    private function sendSuccessMessage(string|int $chatId, string $walletAddress): void
    {
        $botClient = new TelegramBotClient();

        $shortAddress = $this->shortenAddress($walletAddress);

        $message = "✅ **Wallet Address Added Successfully** ✅\n\n"
            . "Your TON wallet address has been saved:\n"
            . "`{$shortAddress}`\n\n"
            . "**Full Address:**\n"
            . "`{$walletAddress}`\n\n"
            . "🎉 **You can now:**\n"
            . "• Make withdrawals\n"
            . "• Exchange stars for TON\n"
            . "• Receive payments\n\n"
            . "💡 *You can update your wallet address anytime using /show_wallet*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💫 Make Withdrawal',
                        'callback_data' => 'withdrawal',
                    ],
                ],
                [
                    [
                        'text' => '💰 Buy Stars',
                        'callback_data' => 'show_buy_stars_menu',
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send error message
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @param  string  $errorMessage  The error message to display
     * @return void
     */
    private function sendErrorMessage(string|int $chatId, string $errorMessage): void
    {
        $botClient = new TelegramBotClient();

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            '❌ ' . $errorMessage,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Shorten wallet address for display
     *
     * @param  string  $address  The full wallet address
     * @return string The shortened address
     */
    private function shortenAddress(string $address): string
    {
        if (strlen($address) <= 20) {
            return $address;
        }

        return substr($address, 0, 10) . '...' . substr($address, -8);
    }
}