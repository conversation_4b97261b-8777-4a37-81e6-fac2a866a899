<?php

namespace Modules\TelegramIstar\TelegramStateHandles;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;
use <PERSON><PERSON>les\TelegramBot\Services\MessageStateService;
use Mo<PERSON>les\TelegramIstar\TelegramCommands\ShowStarShopCommand;
use Mo<PERSON>les\TelegramBot\Interfaces\StateHandleInterface;

class BuyStarsForOthersHandle implements StateHandleInterface
{
    /**
     * Handle the username processing for buying stars for others
     */
    public function handle(string $text, string|int $chatId, TelegramUser $user): void
    {
        try {
            $username = trim($text);

            if (!$username) {
                $this->sendErrorMessage($chatId, 'Please provide a valid username.');
                return;
            }

            // Validate username format
            if (!$username) {
                $this->sendInvalidUsernameMessage($chatId);
                return;
            }

            // Remove @ symbol if present
            $username = ltrim($username, '@');

            DiscordLogHelper::log("Processing buy stars for others - username: {$username} for user {$user->tele_id}");

            // Use the existing ShowStarShopCommand to display the star shop
            $showStarShopCommand = new ShowStarShopCommand();
            $showStarShopCommand->handle($chatId, $user, [$username]);

            // Clear user state after showing the shop
            $stateService = new MessageStateService();
            $stateService->clearUserState($user);

        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyForOthersUsernameHandle failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $this->sendErrorMessage($chatId, 'An error occurred while processing your request.');
        }
    }

    /**
     * Send invalid username message
     */
    private function sendInvalidUsernameMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient;

        $message = "❌ **Invalid Format!**\n\n"
            ."Please use the correct username format:\n"
            ."**@username**\n\n"
            ."*Example: @johndoe*\n\n";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId, string $errorMessage): void
    {
        $botClient = new TelegramBotClient;

        $botClient->sendMessage(
            $chatId,
            '❌ '.$errorMessage,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
