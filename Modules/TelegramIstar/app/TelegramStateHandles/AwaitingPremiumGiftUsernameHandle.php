<?php

namespace Modules\TelegramIstar\TelegramStateHandles;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramBot\Interfaces\StateHandleInterface;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;
use Modules\TelegramIstar\Services\PremiumService;
use Modules\TelegramIstar\Components\BackToMenu;

class AwaitingPremiumGiftUsernameHandle implements StateHandleInterface
{
    /**
     * Handle the username processing for buying Telegram Premium for others
     */
    public function handle(string $text, string|int $chatId, TelegramUser $user): void
    {
        try {
            $username = trim($text);

            if (!$username) {
                $this->sendErrorMessage($chatId, 'Please provide a valid username.');
                return;
            }

            // Remove @ symbol if present
            $username = ltrim($username, '@');

            // Basic username validation
            if (strlen($username) < 3 || !preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
                $this->sendInvalidUsernameMessage($chatId);
                return;
            }

            DiscordLogHelper::log("Processing buy Telegram Premium for others - username: {$username} for user {$user->tele_id}");

            $this->showPremiumShopForUser($chatId, $user, $username);

        } catch (\Exception $e) {
            DiscordLogHelper::error('AwaitingPremiumGiftUsernameHandle failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $this->sendErrorMessage($chatId, 'An error occurred while processing your request.');
        }
    }

    /**
     * Show premium shop for buying for specific user
     */
    private function showPremiumShopForUser(string|int $chatId, TelegramUser $user, string $targetUsername): void
    {
        try {
            $botClient = new TelegramBotClient;

            // Create description with target username
            $description = $this->getPremiumShopDescription($targetUsername);

            // Create keyboard with modified callback data to include target username
            $keyboard = $this->createPremiumShopKeyboard($targetUsername);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            // Clear user state
            $stateService = new MessageStateService();
            $stateService->clearUserState($user);

        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to show premium shop for user: '.$e->getMessage());
            $this->sendErrorMessage($chatId, 'Failed to process your request. Please try again.');
        }
    }

    /**
     * Get the Telegram Premium shop description for buying for others
     */
    private function getPremiumShopDescription(string $targetUsername): string
    {
        $tonHelper = new TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();

        return "🎁 **Buy Telegram Premium for @{$targetUsername}** 🎁\n\n"
            . "💰 Choose a Premium Package:\n\n"
            . "🌟 **Telegram Premium Benefits:**\n"
            . "• Larger file uploads (up to 4GB)\n"
            . "• Faster downloads & priority support\n"
            . "• Exclusive stickers, reactions & themes\n"
            . "• Advanced chat management tools\n"
            . "• Voice-to-text conversion\n\n"
            . '💎 Current TON Rate: $'.number_format($tonRate, 4)."\n"
            . "🔥 Payment method: TON only\n\n"
            . 'Select a package below:';
    }

    /**
     * Create the inline keyboard for premium shop menu (for gifting)
     */
    private function createPremiumShopKeyboard(string $targetUsername): array
    {
        $inlineKeyboard = [];

        $tonRate = (new TonHelper)->fetchTonPrice();
        $premiumService = app(PremiumService::class);
        $premiumPackages = $premiumService->getPremiumPackages();

        // Create package buttons
        foreach ($premiumPackages as $package) {
            $tonPrice = number_format($package['usd_price'] / $tonRate, 4);
            $periodText = $premiumService->getPeriodText($package['days']);
            $buttonText = "👑 {$periodText} - {$tonPrice} TON (\${$package['usd_price']})";

            $callbackData = 'buy_premium-'.$package['days'].'-'.$targetUsername;
            
            $inlineKeyboard[] = [
                [
                    'text' => $buttonText,
                    'callback_data' => $callbackData,
                ],
            ];
        }

        // Add back button
        $inlineKeyboard[] = [
            BackToMenu::make(),
        ];

        return ['inline_keyboard' => $inlineKeyboard];
    }

    /**
     * Send invalid username message
     */
    private function sendInvalidUsernameMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient;

        $message = "❌ **Invalid Username Format!**\n\n"
            . "Please use a valid Telegram username format:\n"
            . "**@username** or **username**\n\n"
            . "*Example: @johndoe or johndoe*\n\n"
            . "Username must be at least 3 characters long and contain only letters, numbers, and underscores.";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId, string $errorMessage): void
    {
        $botClient = new TelegramBotClient;

        $botClient->sendMessage(
            $chatId,
            '❌ '.$errorMessage,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
