<?php

namespace Modules\TelegramIstar\TelegramStateHandles;

use App\Helpers\DiscordLogHelper;
use App\Models\Setting;
use Modules\TelegramBot\Interfaces\StateHandleInterface;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageStateService;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\WithdrawalType;

class WithdrawalAmountHandle implements StateHandleInterface
{
    /**
     * Handle the withdrawal amount processing
     */
    public function handle(string $text, string|int $chatId, TelegramUser $user): void
    {
        try {
            $amount = trim($text);

            // Validate amount is numeric
            if (!is_numeric($amount)) {
                $this->sendInvalidAmountMessage($chatId);
                return;
            }

            $amount = (float) $amount;

            // Validate amount is positive
            if ($amount <= 0) {
                $this->sendInvalidAmountMessage($chatId);
                return;
            }

            // Check if user has sufficient balance
            $userBalance = $user->getBalance('STAR');
            if ($amount > $userBalance) {
                $this->sendInsufficientBalanceMessage($chatId, $userBalance);
                return;
            }

            DiscordLogHelper::log("Processing star withdrawal amount: {$amount} for user {$user->tele_id}");

            // Show confirmation prompt
            $this->showConfirmationPrompt($chatId, $user, $amount);

        } catch (\Exception $e) {
            DiscordLogHelper::error('WithdrawalAmountHandle failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());
            
            $this->sendErrorMessage($chatId);
        }
    }

    /**
     * Show confirmation prompt with transaction details
     */
    private function showConfirmationPrompt(string|int $chatId, TelegramUser $user, float $amount): void
    {
        $botClient = new TelegramBotClient();
        
        // Calculate TON equivalent
        $exchangeRate = $this->getExchangeRate();
        $tonRate = $this->getTonRate();
        $usdAmount = $amount * $exchangeRate;
        $tonAmount = $usdAmount / $tonRate;

        $message = "⭐ **Withdrawal Confirmation** ⭐\n\n"
            . "📊 **Transaction Details:**\n"
            . "• Stars to exchange: **" . number_format($amount, 2) . " ⭐**\n"
            . "• USD equivalent: **\$" . number_format($usdAmount, 2) . "**\n"
            . "• TON to receive: **" . number_format($tonAmount, 4) . " TON**\n\n"
            . "⚠️ **Important:**\n"
            . "• Your stars will be deducted immediately\n"
            . "• TON will be sent after admin approval\n"
            . "• This action cannot be undone\n\n"
            . "Do you want to proceed?";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '✅ Confirm',
                        'callback_data' => 'confirm_withdrawal_' . $amount,
                    ],
                    [
                        'text' => '❌ Cancel',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );

        // Clear user state since we're now using callback data
        $stateService = new MessageStateService();
        $stateService->clearUserState($user);
    }

    /**
     * Send invalid amount message
     */
    private function sendInvalidAmountMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();
        
        $message = "❌ **Invalid Amount**\n\n"
            . "Please enter a valid positive number.\n\n"
            . "Example: `100` or `50.5`\n\n"
            . "Try again:";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send insufficient balance message
     */
    private function sendInsufficientBalanceMessage(string|int $chatId, float $userBalance): void
    {
        $botClient = new TelegramBotClient();
        
        $message = "❌ **Insufficient Balance**\n\n"
            . "You only have **" . number_format($userBalance, 2) . " ⭐** in your account.\n\n"
            . "Please enter an amount up to " . number_format($userBalance, 2) . " stars.\n\n"
            . "Try again:";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send generic error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();
        
        $message = "❌ An error occurred while processing your request. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );

        // Clear user state
        $stateService = new MessageStateService();
        $stateService->clearUserState(TelegramUser::where('tele_id', $chatId)->first());
    }

    /**
     * Get exchange rate from settings
     */
    private function getExchangeRate(): float
    {
        try {
            $rate = Setting::get('star_conversion_rate', 0.013);
            return (float) $rate;
        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to get star conversion rate: ' . $e->getMessage());
            return 0.013; // Default fallback
        }
    }

    /**
     * Get TON rate (placeholder - would need real implementation)
     */
    private function getTonRate(): float
    {
        // This is a placeholder. In a real implementation, you would:
        // 1. Get current TON/USD rate from an API
        // 2. Store it in settings
        // 3. Update it periodically
        return 2.5; // Assuming 1 USD = 2.5 TON (placeholder)
    }
}
