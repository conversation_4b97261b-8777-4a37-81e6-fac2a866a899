<?php

namespace Modules\TelegramIstar\Observers;

use <PERSON><PERSON><PERSON>\TelegramIstar\Models\Withdrawal;
use Mo<PERSON>les\TelegramIstar\Services\AdminNotificationService;
use Illuminate\Support\Facades\Log;

class WithdrawalObserver
{
    protected AdminNotificationService $notificationService;

    public function __construct(AdminNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the Withdrawal "created" event.
     */
    public function created(Withdrawal $withdrawal): void
    {
        try {
            // Send admin notification for all withdrawal types
            $this->notificationService->sendWithdrawalNotification($withdrawal);

            Log::info('Admin notification sent for withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'type' => $withdrawal->type,
                'user_id' => $withdrawal->user_id,
                'amount' => $withdrawal->amount
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send admin notification for withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle the Withdrawal "updated" event.
     */
    public function updated(Withdrawal $withdrawal): void {}

    /**
     * Handle the Withdrawal "deleted" event.
     */
    public function deleted(Withdrawal $withdrawal): void {}

    /**
     * Handle the Withdrawal "restored" event.
     */
    public function restored(Withdrawal $withdrawal): void {}

    /**
     * Handle the Withdrawal "force deleted" event.
     */
    public function forceDeleted(Withdrawal $withdrawal): void {}
}