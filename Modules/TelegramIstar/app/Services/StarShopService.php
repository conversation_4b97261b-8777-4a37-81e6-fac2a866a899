<?php

namespace Modules\TelegramIstar\Services;

use App\Services\SettingsService;
use App\Helpers\TonHelper;

class StarShopService
{
    /**
     * Star to USD mapping based on Telegram's pricing
     */
    public const STAR_TO_USD_MAP = [
        50,
        75,
        100,
        150,
        250,
        350,
        500,
        750,
        1000,
        1500,
        2500,
        5000,
        10000,
        25000,
        35000,
        50000,
        100000,
        150000,
        500000,
        1000000,
    ];

    /**
     * Get all star packages sorted by star amount
     */
    public function getStarPackages(): array
    {
        $packages = [];

        $settingService = app(SettingsService::class);
        $conversionRate = $settingService->getSetting('star_conversion_rate');

        foreach (self::STAR_TO_USD_MAP as $stars) {
            $packages[] = [
                'stars' => $stars,
                'usd_price' => $stars * $conversionRate,
            ];
        }

        return $packages;
    }

    /**
     * Calculate TON price for a given USD amount
     */
    public function calculateTonPrice(int $starAmount): float
    {
        $tonHelper = new TonHelper;

        $tonRate = $tonHelper->fetchTonPrice();

        $usdPrice = $this->calculateUsdPrice($starAmount);
        $tonPrice = $usdPrice / $tonRate;

        return $tonPrice;
    }

    public function tonPriceForView(float $usdPrice): string
    {
        $tonPrice = $this->calculateTonPrice($usdPrice);

        return number_format($tonPrice, 4);
    }

    public function calculateUsdPrice(int $starAmount): float
    {
        $settingService = app(SettingsService::class);
        $conversionRate = $settingService->getSetting('star_conversion_rate');
        $usdPrice = $starAmount * $conversionRate;

        return $usdPrice;
    }
}
