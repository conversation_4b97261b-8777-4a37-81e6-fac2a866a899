<?php

namespace Modules\TelegramIstar\Services;

use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Modules\TelegramBot\Models\TelegramUser;
use <PERSON><PERSON><PERSON>\TelegramIstar\Enums\WithdrawalType;

class AdminNotificationService
{
    protected TelegramBotClient $botClient;

    public function __construct()
    {
        $this->botClient = new TelegramBotClient();
    }

    /**
     * Send withdrawal notification to all admins
     *
     * @param Withdrawal $withdrawal
     * @return void
     */
    public function sendWithdrawalNotification(Withdrawal $withdrawal): void
    {
        try {
            $user = $withdrawal->user;
            if (!$user) {
                Log::warning('Cannot send withdrawal notification: User not found', [
                    'withdrawal_id' => $withdrawal->id,
                    'user_id' => $withdrawal->user_id
                ]);
                return;
            }

            $adminIds = $this->getAdminIds();
            if (empty($adminIds)) {
                Log::warning('No admin IDs configured for withdrawal notifications');
                return;
            }

            $message = $this->buildWithdrawalMessage($withdrawal, $user);
            $keyboard = $this->buildWithdrawalKeyboard($withdrawal);

            foreach ($adminIds as $adminId) {
                try {
                    $this->botClient->sendMessage(
                        chatId: $adminId,
                        text: $message,
                        options: [
                            'reply_markup' => json_encode($keyboard),
                            'parse_mode' => 'HTML'
                        ]
                    );
                } catch (\Exception $e) {
                    Log::error('Failed to send withdrawal notification to admin', [
                        'admin_id' => $adminId,
                        'withdrawal_id' => $withdrawal->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to send withdrawal notifications', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get admin IDs from environment variable
     *
     * @return array
     */
    protected function getAdminIds(): array
    {
        $adminIds = env('TELEGRAM_ADMIN_IDS', '');
        if (empty($adminIds)) {
            return [];
        }

        return array_filter(explode(',', $adminIds));
    }

    /**
     * Build withdrawal notification message
     *
     * @param Withdrawal $withdrawal
     * @param TelegramUser $user
     * @return string
     */
    protected function buildWithdrawalMessage(Withdrawal $withdrawal, TelegramUser $user): string
    {
        $now = now()->format('Y-m-d H:i:s');
        $userMention = $user->username ? '@' . htmlspecialchars($user->username) : htmlspecialchars($user->name);

        // Get withdrawal type display name
        $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);

        // Get withdrawal type emoji
        $emoji = $this->getWithdrawalTypeEmoji($withdrawal->type);

        $message = __('telegramistar::notifications.withdrawal.new_request', [
            'emoji' => $emoji,
            'type' => $typeDisplay,
            'user_mention' => $userMention,
            'user_id' => $user->tele_id,
            'amount' => number_format($withdrawal->amount, 8),
            'address' => htmlspecialchars($withdrawal->address),
            'time' => $now,
            'status' => ucfirst($withdrawal->status)
        ]);

        // Add note if present
        if ($withdrawal->note) {
            $message .= "\n" . __('telegramistar::notifications.withdrawal.note', [
                'note' => htmlspecialchars($withdrawal->note)
            ]);
        }

        $message .= "\n\n" . __('telegramistar::notifications.withdrawal.action_required');

        return $message;
    }

    /**
     * Build withdrawal action keyboard
     *
     * @param Withdrawal $withdrawal
     * @return array
     */
    protected function buildWithdrawalKeyboard(Withdrawal $withdrawal): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => __('telegramistar::notifications.buttons.approve'),
                        'callback_data' => "approve_withdrawal-{$withdrawal->id}"
                    ],
                    [
                        'text' => __('telegramistar::notifications.buttons.reject'),
                        'callback_data' => "reject_withdrawal-{$withdrawal->id}"
                    ]
                ]
            ]
        ];
    }

    /**
     * Get withdrawal type display name
     *
     * @param string $type
     * @return string
     */
    protected function getWithdrawalTypeDisplay(string $type): string
    {
        return match ($type) {
            WithdrawalType::STAR_PURCHASE => __('telegramistar::notifications.types.star_purchase'),
            WithdrawalType::PREMIUM_PURCHASE => __('telegramistar::notifications.types.premium_purchase'),
            WithdrawalType::STAR_WITHDRAWAL => __('telegramistar::notifications.types.star_withdrawal'),
            WithdrawalType::TON_WITHDRAWAL => __('telegramistar::notifications.types.ton_withdrawal'),
            WithdrawalType::EXCHANGE_STAR => 'Star to TON Exchange',
            default => __('telegramistar::notifications.types.unknown')
        };
    }

    /**
     * Get withdrawal type emoji
     *
     * @param string $type
     * @return string
     */
    protected function getWithdrawalTypeEmoji(string $type): string
    {
        return match ($type) {
            WithdrawalType::STAR_PURCHASE => '⭐',
            WithdrawalType::PREMIUM_PURCHASE => '🔰',
            WithdrawalType::STAR_WITHDRAWAL => '💫',
            WithdrawalType::TON_WITHDRAWAL => '💎',
            WithdrawalType::EXCHANGE_STAR => '🔄',
            default => '📤'
        };
    }
}