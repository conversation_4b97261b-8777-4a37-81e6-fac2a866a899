<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class BuyStarsForOthersCommand implements CommandInterface
{
    /**
     * Handle the buy for others command (callback from button)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing buy for others prompt to user '.$user->tele_id);

            $message = $this->getBuyForOthersMessage();
            $keyboard = $this->createBackKeyboard($user);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            // Store user state for next message handling
            $messageStateService = new MessageStateService;
            $messageStateService->setUserState($user, 'buy_stars_for_others');

            return ['success' => true, 'handled' => true];
        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyStarsForOthersCommand failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }

    /**
     * Get the buy stars for others message
     */
    private function getBuyForOthersMessage(): string
    {
        return "🎁 **Buy for Others** 🎁\n\n"
            ."Please send the Telegram username of the person you want to buy for.\n\n"
            ."**Format:** @username\n\n"
            ."*Example: @johndoe*";
    }

    /**
     * Create the back keyboard
     */
    private function createBackKeyboard(): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back',
                        'callback_data' => 'show_buy_stars_menu',
                    ],
                ],
            ],
        ];
    }
}
