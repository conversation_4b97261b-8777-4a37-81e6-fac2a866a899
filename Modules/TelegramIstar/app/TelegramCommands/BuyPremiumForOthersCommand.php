<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class BuyPremiumForOthersCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $botClient = new TelegramBotClient;
            $messageStateService = new MessageStateService;

            DiscordLogHelper::log('Processing buy Telegram Premium for others request for user ' . $user->tele_id);

            // Set user state to expect username input
            $messageStateService->setUserState($user, 'awaiting_premium_gift_username');

            $botClient->sendMessage(
                $chatId,
                "🎁 **Gift Telegram Premium**\n\n"
                . "Please enter the username of the person you want to gift Telegram Premium to:\n\n"
                . "📝 Format: @username or just username\n"
                . "⚠️ Make sure the username is correct as this cannot be changed later.\n\n"
                . "Type /cancel to cancel this operation.",
                [
                    'parse_mode' => 'Markdown',
                ]
            );

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyPremiumForOthersCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $botClient = new TelegramBotClient;
            $botClient->sendMessage(
                $chatId,
                "❌ An error occurred while processing your request. Please try again later.",
                []
            );

            return ['success' => true, 'handled' => true];
        }
    }
}
