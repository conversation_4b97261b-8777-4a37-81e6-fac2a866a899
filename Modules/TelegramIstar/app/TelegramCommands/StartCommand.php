<?php

namespace Modules\TelegramIstar\TelegramCommands;

use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramIstar\Services\ThemeService;
use Modules\TelegramIstar\Views\StartMenuView;

class StartCommand implements CommandInterface
{
    protected $mainMenuService;

    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the /start command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $view = new StartMenuView;
        $view->show($chatId, $user);

        return ['success' => true, 'handled' => true];
    }
}
