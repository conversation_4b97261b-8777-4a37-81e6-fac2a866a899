<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class ChangeWalletCommand implements CommandInterface
{
    /**
     * Handle the change_wallet callback
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who clicked the button
     * @param  array|null  $params  Additional command parameters
     * @return array Response with success status and handled flag
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing change_wallet callback for user ' . $user->tele_id);

            // Check if user has a wallet address
            if (!$this->hasWalletAddress($user)) {
                $this->sendNoWalletMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            $botClient = new TelegramBotClient();

            $currentAddress = $user->wallet_address;
            $shortAddress = $this->shortenAddress($currentAddress);

            $message = "🔄 **Change Wallet Address** 🔄\n\n"
                . "**Current Address:**\n"
                . "`{$shortAddress}`\n\n"
                . "**Full Address:**\n"
                . "`{$currentAddress}`\n\n"
                . "Please send your new TON wallet address in the chat.\n\n"
                . "**Supported formats:**\n"
                . "• User-friendly: `UQAbc...` or `EQAbc...`\n"
                . "• Raw format: `0:1234abcd...`\n\n"
                . "💡 *You can copy your address from your TON wallet app.*\n\n"
                . "Send your new wallet address now:";

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'parse_mode' => 'Markdown',
                ]
            );

            // Set user state to expect wallet address input
            $messageStateService = new MessageStateService();
            $messageStateService->setUserState($user, 'add_wallet');

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('ChangeWalletCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }

    /**
     * Check if user has a wallet address configured
     *
     * @param  TelegramUser  $user  The user to check
     * @return bool True if user has wallet address
     */
    private function hasWalletAddress(TelegramUser $user): bool
    {
        return !empty($user->wallet_address);
    }

    /**
     * Send message when user has no wallet to change
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @return void
     */
    private function sendNoWalletMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ **No Wallet to Change** ❌\n\n"
            . "You don't have a wallet address configured yet.\n"
            . "Use `/show_wallet` to add your first wallet address.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💳 Add Wallet',
                        'callback_data' => 'add_wallet_prompt',
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Shorten wallet address for display
     *
     * @param  string  $address  The full wallet address
     * @return string The shortened address
     */
    private function shortenAddress(string $address): string
    {
        if (strlen($address) <= 20) {
            return $address;
        }

        return substr($address, 0, 10) . '...' . substr($address, -8);
    }
}