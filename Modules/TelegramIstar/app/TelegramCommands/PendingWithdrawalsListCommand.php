<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramIstar\Models\Withdrawal;
use <PERSON><PERSON><PERSON>\TelegramIstar\Views\PendingWithdrawalsListView;

class PendingWithdrawalsListCommand implements CommandInterface
{
    /**
     * Handle the /pending_withdrawals command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing /pending_withdrawals command for user ' . $user->tele_id);

            $query = Withdrawal::query()
                ->with('user')
                ->where('user_id', $user->id)
                ->orderBy('updated_at', 'desc');

            // Parse page parameter from params (default to page 1)
            $page = isset($params[0]) ? (int) $params[0] : 1;
            $page = max(1, $page); // Ensure page is at least 1

            // Get paginated pending withdrawals
            $perPage = 10;
            $cloneQuery = clone $query;
            $withdrawals = $cloneQuery
                ->paginate($perPage, ['*'], 'page', $page);

            if ($withdrawals->isEmpty() && $page === 1) {
                // No pending withdrawals found
                $this->sendNoWithdrawalsMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            if ($withdrawals->isEmpty()) {
                $cloneQuery = clone $query;
                // Page doesn't exist, redirect to first page
                $withdrawals = $cloneQuery
                    ->paginate($perPage, ['*'], 'page', 1);
                $page = 1;
            }

            // Display the paginated list
            $view = new PendingWithdrawalsListView();
            $view->show($chatId, $user, [
                'withdrawals' => $withdrawals,
                'current_page' => $page,
                'total_pages' => $withdrawals->lastPage(),
                'per_page' => $perPage
            ]);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('PendingWithdrawalsListCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Send message when no pending withdrawals are found
     */
    private function sendNoWithdrawalsMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "📋 **Pending Withdrawals** 📋\n\n"
            . "❌ No pending withdrawal requests found.\n\n"
            . "All withdrawal requests have been processed or there are no outstanding requests at this time.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send generic error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "❌ An error occurred while retrieving pending withdrawals. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
