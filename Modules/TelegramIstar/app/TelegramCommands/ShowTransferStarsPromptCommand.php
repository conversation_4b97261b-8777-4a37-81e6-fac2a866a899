<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramBot\Services\MessageStateService;

class ShowTransferStarsPromptCommand implements CommandInterface
{
    /**
     * Handle the transfer stars command (callback from button)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing transfer stars prompt to user '.$user->tele_id);

            $message = $this->getTransferStarsMessage();

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'parse_mode' => 'Markdown',
                ]
            );

            // Store user state for next message handling
            $messageStateService = new MessageStateService;
            $messageStateService->setUserState($user, 'transfer_stars');

            return ['success' => true, 'handled' => true];
        } catch (\Exception $e) {
            DiscordLogHelper::error('TransferStarsCommand failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }

    /**
     * Get the transfer stars message
     */
    private function getTransferStarsMessage(): string
    {
        return "Top-up Menu ⭐️ for your account\n\n"
            ."Enter the amount of stars ⭐️ in the chat to top up your balance.\n"
            ."The minimum top-up amount is 5 ⭐️.\n"
            ."For example: 100\n\n"
            .'⚠️ Make sure you have enough stars in Telegram before transferring';
    }
}
