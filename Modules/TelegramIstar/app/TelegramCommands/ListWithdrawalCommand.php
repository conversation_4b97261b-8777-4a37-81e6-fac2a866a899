<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Middlewares\AdminMiddleware;
use Modules\TelegramBot\Models\TelegramUser;
use <PERSON><PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Mo<PERSON>les\TelegramIstar\Services\AdminNotificationService;

class ListWithdrawalCommand implements CommandInterface
{
    protected AdminNotificationService $adminNotificationService;

    public function __construct(AdminNotificationService $adminNotificationService)
    {
        $this->adminNotificationService = $adminNotificationService;
    }

    /**
     * Handle the /list_withdrawals command for admin users
     *
     * Retrieves up to 3 pending withdrawal records ordered by creation date (oldest first)
     * and sends withdrawal notifications to all configured admins for each record.
     *
     * @param  string|int  $chatId  The Telegram chat ID where the command was issued
     * @param  TelegramUser  $user  The user who sent the command
     * @param  array|null  $params  Additional command parameters (unused)
     * @return array Response array with success status and handled flag
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user) {
            try {
                DiscordLogHelper::log('Processing /list_withdrawals command for admin user '.$user->tele_id);

                // Query up to 3 pending withdrawals ordered by created_at ascending (oldest first)
                $withdrawals = Withdrawal::where('status', 'pending')
                    ->with('user')
                    ->orderBy('created_at', 'asc')
                    ->limit(3)
                    ->get();

                if ($withdrawals->isEmpty()) {
                    DiscordLogHelper::log('No pending withdrawals found for admin notification');
                    $this->sendNoWithdrawalsMessage($chatId);

                    return ['success' => true, 'handled' => true];
                }

                $processedCount = 0;
                foreach ($withdrawals as $withdrawal) {
                    try {
                        // Send withdrawal notification to all admins
                        $this->adminNotificationService->sendWithdrawalNotification($withdrawal);
                        $processedCount++;

                        DiscordLogHelper::log('Sent withdrawal notification for withdrawal ID '.$withdrawal->id);
                    } catch (\Exception $e) {
                        DiscordLogHelper::error('Failed to send notification for withdrawal ID '.$withdrawal->id.': '.$e->getMessage());
                        // Continue processing other withdrawals even if one fails
                    }
                }

                DiscordLogHelper::log('Successfully processed '.$processedCount.' withdrawal notifications for admin '.$user->tele_id);

                return ['success' => true, 'handled' => true];

            } catch (\Exception $e) {
                DiscordLogHelper::error('ListWithdrawalCommand failed: '.$e->getMessage());
                DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

                $this->sendErrorMessage($chatId);

                return ['success' => false, 'handled' => false];
            }
        });
    }

    /**
     * Send message when no pending withdrawals are found
     *
     * @param  string|int  $chatId  The chat ID to send the message to
     */
    private function sendNoWithdrawalsMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient;

        $message = "📋 **Withdrawal Notifications** 📋\n\n"
            ."❌ No pending withdrawal requests found.\n\n"
            .'All withdrawal requests have been processed or there are no outstanding requests at this time.';

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send generic error message
     *
     * @param  string|int  $chatId  The chat ID to send the message to
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient;

        $message = '❌ An error occurred while processing withdrawal notifications. Please try again later.';

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
