<?php

namespace Modules\TelegramIstar\TelegramCommands;

use <PERSON><PERSON><PERSON>\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramIstar\Views\StarShopMenuView;

class ShowStarShopCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $view = new StarShopMenuView;
        $view->show($chatId, $user, $params);

        return ['success' => true, 'handled' => true];
    }
}
