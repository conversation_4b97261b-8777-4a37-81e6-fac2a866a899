<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class AddWalletPromptCommand implements CommandInterface
{
    /**
     * Handle the add_wallet_prompt callback
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who clicked the button
     * @param  array|null  $params  Additional command parameters
     * @return array Response with success status and handled flag
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing add_wallet_prompt callback for user ' . $user->tele_id);

            $botClient = new TelegramBotClient();

            $message = "💳 **Add Your Wallet Address** 💳\n\n"
                . "Please send your TON wallet address in the chat.\n\n"
                . "**Supported formats:**\n"
                . "• User-friendly: `UQAbc...` or `EQAbc...`\n"
                . "• Raw format: `0:1234abcd...`\n\n"
                . "💡 *You can copy your address from your TON wallet app.*\n\n"
                . "Send your wallet address now:";

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'parse_mode' => 'Markdown',
                ]
            );

            // Set user state to expect wallet address input
            $messageStateService = new MessageStateService();
            $messageStateService->setUserState($user, 'add_wallet');

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('AddWalletPromptCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }
}