<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramTonPayment\Models\VerificationTransaction;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;

class CancelBuyCommand implements CommandInterface
{
    /**
     * Handle the cancel buy command (callback from payment confirmation)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $transactionId = $params[0] ?? null;
        $backToCommand = $params[1] ?? 'start';

        if (!$transactionId) {
            DiscordLogHelper::error('Cancel buy command called without transaction ID for user ' . $user->tele_id);
            return ['success' => true, 'handled' => true];
        }

        $transaction = VerificationTransaction::query()
            ->where('id', $transactionId)
            ->where('user_id', $user->id)
            ->first();

        if (!$transaction) {
            DiscordLogHelper::error('Cancel buy command called with invalid transaction ID for user ' . $user->tele_id);
            return ['success' => true, 'handled' => true];
        }

        $transaction->status = 'cancelled';
        $transaction->save();

        $botClient = new TelegramBotClient;
        $botClient->sendMessage(
            $chatId,
            "Your purchase has been cancelled. You can return to the menu.",
            [
                'reply_markup' => [
                    'inline_keyboard' => [
                        [
                            ['text' => '🔙 Back', 'callback_data' => $backToCommand],
                        ],
                    ],
                ],
            ]
        );

        return [
            'success' => true,
            'handled' => true,
        ];
    }
}
