<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramIstar\Views\WithdrawalView;

class WithdrawalCommand implements CommandInterface
{
    /**
     * Handle the /withdrawal command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing /withdrawal command for user ' . $user->tele_id);

            // Check if user has stars in their balance
            $starBalance = $user->getBalance('STAR');
            if ($starBalance <= 0) {
                $this->sendNoStarsMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            // Check if user has a wallet address configured
            // Since there's no wallet address field in TelegramUser, we'll show a message
            // indicating they need to add a wallet address first
            if (!$this->hasWalletAddress($user)) {
                $this->sendNoWalletMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            // Show the withdrawal interface
            $view = new WithdrawalView();
            $view->show($chatId, $user);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('WithdrawalCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Send message when user has no stars
     */
    private function sendNoStarsMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "⭐ **Star to TON Exchange** ⭐\n\n"
            . "❌ You don't have any stars. Please run `/show_deposit` to add stars to your account.\n\n"
            . "💡 *Tip: You can buy stars and then exchange them for TON!*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💰 Buy Stars',
                        'callback_data' => 'show_buy_stars_menu',
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send message when user has no wallet address
     */
    private function sendNoWalletMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "⭐ **Star to TON Exchange** ⭐\n\n"
            . "❌ Please add your wallet address first by running `/add_wallet [address]`\n\n"
            . "💡 *Example: `/add_wallet UQALKPk4q6mOGYCQq8ujB1ne4BDQixoG1RZRruodP-AKBu6R`*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💳 Add Wallet',
                        'callback_data' => 'add_wallet_help',
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send generic error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "❌ An error occurred while processing your withdrawal request. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Check if user has a wallet address configured
     */
    private function hasWalletAddress(TelegramUser $user): bool
    {
        return !empty($user->wallet_address);
    }
}
