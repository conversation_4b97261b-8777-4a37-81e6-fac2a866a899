<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramBot\Services\MessageStateService;
use Mo<PERSON>les\TelegramIstar\Views\ShowWalletView;

class ShowWalletCommand implements CommandInterface
{
    /**
     * Handle the /show_wallet command
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who sent the command
     * @param  array|null  $params  Additional command parameters
     * @return array Response with success status and handled flag
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing /show_wallet command for user ' . $user->tele_id);

            // Check if user has a wallet address configured
            if ($this->hasWalletAddress($user)) {
                // Show wallet information with change option
                $view = new ShowWalletView();
                $view->showWalletInfo($chatId, $user);
            } else {
                // Show prompt to add wallet address
                $view = new ShowWalletView();
                $view->showAddWalletPrompt($chatId, $user);
            }

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('ShowWalletCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Check if user has a wallet address configured
     *
     * @param  TelegramUser  $user  The user to check
     * @return bool True if user has wallet address
     */
    private function hasWalletAddress(TelegramUser $user): bool
    {
        return !empty($user->wallet_address);
    }

    /**
     * Send error message when command fails
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @return void
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ An error occurred while processing your wallet request. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}