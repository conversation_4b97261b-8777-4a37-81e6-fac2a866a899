<?php

namespace Modules\TelegramIstar\TelegramCommands;

use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramIstar\Views\DepositMenuView;

class ShowDepositCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $view = new DepositMenuView;
        $view->show($chatId, $user);

        return ['success' => true, 'handled' => true];
    }
}
