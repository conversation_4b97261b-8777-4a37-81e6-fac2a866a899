<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramIstar\Views\VipStatusView;

class VipStatusCommand implements CommandInterface
{
    /**
     * Handle the VIP status command
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who sent the command
     * @param  array|null  $params  Additional command parameters
     * @return array Response with success status and handled flag
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing VIP status command for user ' . $user->tele_id);

            $view = new VipStatusView();
            $view->show($chatId, $user, $params);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('VipStatusCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            return ['success' => true, 'handled' => true];
        }
    }
}
