<?php

namespace Modules\TelegramIstar\TelegramCommands;

use <PERSON><PERSON><PERSON>\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramIstar\Views\BuyPremiumMenuView;

class ShowBuyPremiumMenuCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $view = new BuyPremiumMenuView;
        $view->show($chatId, $user);

        return ['success' => true, 'handled' => true];
    }
}
