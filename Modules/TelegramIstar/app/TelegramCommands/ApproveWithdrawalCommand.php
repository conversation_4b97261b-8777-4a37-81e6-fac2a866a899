<?php

namespace Mo<PERSON>les\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageContextService;
use Mo<PERSON>les\TelegramBot\Middlewares\AdminMiddleware;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\WithdrawalType;

class ApproveWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (!$withdrawalId) {
                DiscordLogHelper::error('ApproveWithdrawal: Missing withdrawal ID in parameters');
                return ['success' => false, 'handled' => false];
            }

            try {
                $withdrawal = Withdrawal::find($withdrawalId);
                if (!$withdrawal) {
                    DiscordLogHelper::error('ApproveWithdrawal: Withdrawal not found', ['id' => $withdrawalId]);
                    $this->sendNotFoundMessage($chatId, $withdrawalId);
                    return ['success' => true, 'handled' => true];
                }

                if ($withdrawal->status !== 'pending') {
                    DiscordLogHelper::error('ApproveWithdrawal: Withdrawal not pending', [
                        'id' => $withdrawalId,
                        'status' => $withdrawal->status
                    ]);
                    $this->sendInvalidStatusMessage($chatId, $withdrawalId, $withdrawal->status);
                    return ['success' => true, 'handled' => true];
                }

                // Update withdrawal status
                $withdrawal->update(['status' => 'approved']);

                DiscordLogHelper::log('Approved withdrawal ID ' . $withdrawalId . ' by admin ' . $user->tele_id);

                // Send confirmation to admin
                $this->sendApprovalConfirmation($chatId, $withdrawal);

                // Notify user about approval
                $this->notifyUserApproval($withdrawal);

                // Delete the original notification message
                MessageContextService::deleteOriginalMessage(new TelegramBotClient(), $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('ApproveWithdrawal failed: ' . $e->getMessage());
                DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

                $this->sendErrorMessage($chatId);
                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }

    /**
     * Send approval confirmation to admin
     */
    private function sendApprovalConfirmation(string|int $chatId, Withdrawal $withdrawal): void
    {
        $botClient = new TelegramBotClient();

        $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);
        $message = "✅ **Withdrawal Approved**\n\n"
            . "📋 **Details:**\n"
            . "• ID: #{$withdrawal->id}\n"
            . "• Type: {$typeDisplay}\n"
            . "• Amount: " . number_format($withdrawal->amount, 2) . "\n"
            . "• User: " . ($withdrawal->user->username ? '@' . $withdrawal->user->username : $withdrawal->user->name) . "\n\n"
            . "✅ The withdrawal has been approved and the user has been notified.";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Notify user about approval
     */
    private function notifyUserApproval(Withdrawal $withdrawal): void
    {
        try {
            $botClient = new TelegramBotClient();
            $user = $withdrawal->user;

            $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);
            $message = "✅ **Withdrawal Approved**\n\n"
                . "🎉 Great news! Your withdrawal request has been approved.\n\n"
                . "📋 **Details:**\n"
                . "• Request ID: #{$withdrawal->id}\n"
                . "• Type: {$typeDisplay}\n"
                . "• Amount: " . number_format($withdrawal->amount, 2) . "\n"
                . "• Status: **Approved**\n\n"
                . "💎 Your TON will be sent to your wallet shortly.\n\n"
                . "Thank you for using our service! 🚀";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🏠 Back to Menu',
                            'callback_data' => 'start',
                        ],
                    ],
                ],
            ];

            $botClient->sendMessage(
                $user->tele_id,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to notify user about approval: ' . $e->getMessage());
        }
    }

    /**
     * Send not found message
     */
    private function sendNotFoundMessage(string|int $chatId, int $withdrawalId): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ Withdrawal ID {$withdrawalId} not found.";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Send invalid status message
     */
    private function sendInvalidStatusMessage(string|int $chatId, int $withdrawalId, string $status): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ Withdrawal ID {$withdrawalId} cannot be approved. Current status: {$status}";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ An error occurred while approving the withdrawal. Please try again.";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Get withdrawal type display name
     */
    private function getWithdrawalTypeDisplay(string $type): string
    {
        return match ($type) {
            WithdrawalType::EXCHANGE_STAR => 'Star to TON Exchange',
            WithdrawalType::STAR_PURCHASE => 'Star Purchase',
            WithdrawalType::PREMIUM_PURCHASE => 'Premium Purchase',
            WithdrawalType::STAR_WITHDRAWAL => 'Star Withdrawal',
            WithdrawalType::TON_WITHDRAWAL => 'TON Withdrawal',
            default => ucfirst(str_replace('_', ' ', $type)),
        };
    }
}
