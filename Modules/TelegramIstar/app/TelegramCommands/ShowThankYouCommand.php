<?php


namespace Modules\TelegramIstar\TelegramCommands;
use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Components\BackToMenu;

class ShowThankYouCommand implements CommandInterface
{
    /**
     * Handle the show thank you command (callback after payment confirmation)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing thank you message to user ' . $user->tele_id);

            // Create and send thank you message
            $message = $this->createThankYouMessage($user);
            $keyboard = $this->createThankYouKeyboard($user);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            DiscordLogHelper::log('Thank you message sent to user ' . $user->tele_id);

            return ['success' => true, 'handled' => true];
        } catch (\Exception $e) {
            DiscordLogHelper::error('ShowThankYouCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }

    /**
     * Create thank you message with multilingual support
     */
    private function createThankYouMessage(TelegramUser $user): string
    {
        // Get user language (default to English)
        $lang = $user->language_code ?? 'en';

        $messages = [
            'en' => "🎉 *Thank You for Your Purchase!*\n\n"
                . "✅ Your payment has been received and is being processed.\n\n"
                . "🔄 Processing usually takes a few minutes. You will receive a notification once we finish.\n\n"
                . "💫 Thank you for choosing our service!\n\n"
                . "❓ If you have any questions or concerns, please contact our support team.",

            'ru' => "🎉 *Спасибо за вашу покупку!*\n\n"
                . "✅ Ваш платёж получен и обрабатывается.\n\n"
                . "🔄 Обработка обычно занимает несколько минут. Вы получите уведомление, как только мы закончим.\n\n"
                . "💫 Спасибо, что выбрали наш сервис!\n\n"
                . "❓ Если у вас есть вопросы, обратитесь в службу поддержки.",
        ];

        return $messages[$lang] ?? $messages['en'];
    }

    /**
     * Create thank you keyboard with navigation options
     */
    private function createThankYouKeyboard(TelegramUser $user): array
    {
        return [
            'inline_keyboard' => [
                [
                    BackToMenu::make(),
                ],
            ],
        ];
    }
}
