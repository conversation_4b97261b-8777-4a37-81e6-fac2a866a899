<?php

namespace Modules\TelegramIstar\Views;

use Mo<PERSON><PERSON>\TelegramBot\Models\TelegramUser;

interface ViewInterface
{
    /**
     * Handle the command execution
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who sent the command
     * @return void
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void;
}
