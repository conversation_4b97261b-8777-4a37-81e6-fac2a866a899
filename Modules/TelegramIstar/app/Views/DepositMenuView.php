<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Components\BackToMenu;
use Mo<PERSON>les\TelegramIstar\Services\ThemeService;

class DepositMenuView implements ViewInterface
{
    protected $mainMenuService;

    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the deposit menu display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing deposit menu to user '.$user->tele_id);

            // Create the deposit menu description
            $description = $this->getDepositDescription();

            // Create inline keyboard for deposit menu
            $keyboard = $this->createDepositKeyboard();

            DiscordLogHelper::log('Sending deposit menu to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('DepositMenuView failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $view = new FallbackView();
            $view->show($chatId, $user);
        }
    }

    /**
     * Get the deposit menu description
     */
    private function getDepositDescription(): string
    {
        return "✨ Deposit Stars to Bot ✨\n\n"
            . "Transfer your stars to the bot to add to your balance.\n\n"
            . "⚠️ Note: Ensure you have sufficient balance before transferring.";
    }

    /**
     * Create the inline keyboard for deposit menu
     */
    private function createDepositKeyboard(): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💰 Transfer stars to bot ⭐',
                        'callback_data' => 'show_transfer_stars_prompt'
                    ]
                ],
                [
                    BackToMenu::make()
                ]
            ]
        ];
    }
}
