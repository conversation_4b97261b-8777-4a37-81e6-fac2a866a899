<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;

class FallbackView implements ViewInterface
{
    protected $themeService;

    public function __construct()
    {
    }

    /**
     * Show the fallback view when no other view matches
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient;
            $botClient->sendMessage(
                $chatId,
                "🚀 Welcome to StarWallet! ⭐\n\nSorry, we're experiencing technical difficulties. Please try again in a moment.",
                ['parse_mode' => 'Markdown']
            );
        } catch (\Exception $fallbackError) {
            DiscordLogHelper::error('Fallback message also failed: ' . $fallbackError->getMessage());
        }
    }
}
