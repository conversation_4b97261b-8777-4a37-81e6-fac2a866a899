<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;

class ShowWalletView implements ViewInterface
{
    /**
     * Show wallet information for user who has a wallet configured
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user whose wallet to show
     * @return void
     */
    public function showWalletInfo(string|int $chatId, TelegramUser $user): void
    {
        try {
            $botClient = new TelegramBotClient();

            $walletAddress = $user->wallet_address;
            $shortAddress = $this->shortenAddress($walletAddress);

            $message = "💳 **Your Wallet** 💳\n\n"
                . "**Current Address:**\n"
                . "`{$shortAddress}`\n\n"
                . "**Full Address:**\n"
                . "`{$walletAddress}`\n\n"
                . "💡 *This address is used for withdrawals and TON payments.*";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔄 Change Wallet',
                            'callback_data' => 'change_wallet',
                        ],
                    ],
                    [
                        [
                            'text' => '💫 Make Withdrawal',
                            'callback_data' => 'withdrawal',
                        ],
                        [
                            'text' => '💰 Buy Stars',
                            'callback_data' => 'show_buy_stars_menu',
                        ],
                    ],
                    [
                        [
                            'text' => '🔙 Back to Menu',
                            'callback_data' => 'start',
                        ],
                    ],
                ],
            ];

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

        } catch (\Exception $e) {
            DiscordLogHelper::error('ShowWalletView.showWalletInfo failed: ' . $e->getMessage());
            $this->sendErrorMessage($chatId, 'Failed to display wallet information.');
        }
    }

    /**
     * Show prompt for user to add wallet address
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who needs to add wallet
     * @return void
     */
    public function showAddWalletPrompt(string|int $chatId, TelegramUser $user): void
    {
        try {
            $botClient = new TelegramBotClient();

            $message = "💳 **Add Wallet Address** 💳\n\n"
                . "❌ **No wallet configured**\n\n"
                . "You need to add a TON wallet address to:\n"
                . "• Make withdrawals\n"
                . "• Exchange stars for TON\n"
                . "• Receive payments\n\n"
                . "**How to add your wallet:**\n"
                . "1. Click the \"Add Wallet\" button below\n"
                . "2. Send your TON wallet address in the chat\n"
                . "3. The bot will save it for future use\n\n"
                . "**Supported formats:**\n"
                . "• User-friendly: `UQ...` or `EQ...`\n"
                . "• Raw format: `0:hex_string`\n\n"
                . "💡 *You can copy your address from your TON wallet app.*";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '💳 Add Wallet',
                            'callback_data' => 'add_wallet_prompt',
                        ],
                    ],
                    [
                        [
                            'text' => '🔙 Back to Menu',
                            'callback_data' => 'start',
                        ],
                    ],
                ],
            ];

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

        } catch (\Exception $e) {
            DiscordLogHelper::error('ShowWalletView.showAddWalletPrompt failed: ' . $e->getMessage());
            $this->sendErrorMessage($chatId, 'Failed to display wallet setup prompt.');
        }
    }

    /**
     * Handle the command execution (required by ViewInterface)
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user who sent the command
     * @param  array|null  $params  Additional command parameters
     * @return void
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        // This method is required by ViewInterface but not used in this implementation
        // The ShowWalletCommand calls the specific methods directly
        $this->showWalletInfo($chatId, $user);
    }

    /**
     * Shorten wallet address for display
     *
     * @param  string  $address  The full wallet address
     * @return string The shortened address
     */
    private function shortenAddress(string $address): string
    {
        if (strlen($address) <= 20) {
            return $address;
        }

        return substr($address, 0, 10) . '...' . substr($address, -8);
    }

    /**
     * Send error message
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @param  string  $errorMessage  The error message to display
     * @return void
     */
    private function sendErrorMessage(string|int $chatId, string $errorMessage): void
    {
        $botClient = new TelegramBotClient();

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            '❌ ' . $errorMessage,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
