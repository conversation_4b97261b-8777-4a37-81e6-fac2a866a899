<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Enums\VipLevel;

class VipStatusView implements ViewInterface
{
    /**
     * Display the user's VIP status information
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user to show VIP status for
     * @param  array|null  $params  Additional parameters
     * @return void
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient();

            DiscordLogHelper::log('Displaying VIP status for user ' . $user->tele_id);

            $message = $this->createVipStatusMessage($user);
            $keyboard = $this->createVipStatusKeyboard();

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

        } catch (\Exception $e) {
            DiscordLogHelper::error('VipStatusView failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
        }
    }

    /**
     * Create the VIP status message
     *
     * @param  TelegramUser  $user  The user to create message for
     * @return string The formatted VIP status message
     */
    private function createVipStatusMessage(TelegramUser $user): string
    {
        $starBalance = $user->balance_star ?? 0;
        $currentLevel = VipLevel::getLevelFromBalance($starBalance);
        $currentEmoji = VipLevel::getEmojiForLevel($currentLevel);
        $progressInfo = VipLevel::getProgressToNextLevel($starBalance, $currentLevel);

        $message = "🌟 *VIP Status Information*\n\n";
        $message .= "👤 *Current VIP Level:* {$currentEmoji} Level {$currentLevel}\n";
        $message .= "⭐ *Your Stars:* " . number_format($starBalance) . "\n\n";

        if ($progressInfo['is_max_level']) {
            $message .= "🎉 *Congratulations!*\n";
            $message .= "You have reached the maximum VIP level!\n";
            $message .= "👑 You are a VIP Level 4 member with all premium benefits.\n\n";
        } else {
            $nextLevel = $currentLevel + 1;
            $nextEmoji = VipLevel::getEmojiForLevel($nextLevel);
            
            $message .= "🎯 *Next Level:* {$nextEmoji} Level {$nextLevel}\n";
            $message .= "📊 *Progress:* {$progressInfo['progress_percentage']}%\n";
            $message .= "⚡ *Stars Needed:* " . number_format($progressInfo['stars_needed']) . "\n\n";
            
            // Progress bar
            $progressBar = $this->createProgressBar($progressInfo['progress_percentage']);
            $message .= "📈 *Progress Bar:*\n{$progressBar}\n\n";
        }

        $message .= $this->getVipBenefitsText($currentLevel);

        return $message;
    }

    /**
     * Create a visual progress bar
     *
     * @param  float  $percentage  The progress percentage (0-100)
     * @return string The progress bar string
     */
    private function createProgressBar(float $percentage): string
    {
        $totalBars = 10;
        $filledBars = (int) round(($percentage / 100) * $totalBars);
        $emptyBars = $totalBars - $filledBars;

        $progressBar = str_repeat('🟩', $filledBars) . str_repeat('⬜', $emptyBars);
        $progressBar .= " {$percentage}%";

        return $progressBar;
    }

    /**
     * Get VIP benefits text based on level
     *
     * @param  int  $level  The VIP level
     * @return string The benefits description
     */
    private function getVipBenefitsText(int $level): string
    {
        $benefits = "🎁 *Your VIP Benefits:*\n";

        switch ($level) {
            case 0:
                $benefits .= "• Basic access to star trading\n";
                $benefits .= "• Standard withdrawal processing\n";
                break;
            case 1:
                $benefits .= "• ⭐ Priority support\n";
                $benefits .= "• Reduced withdrawal fees\n";
                $benefits .= "• Access to exclusive offers\n";
                break;
            case 2:
                $benefits .= "• 🌟 All Level 1 benefits\n";
                $benefits .= "• Faster withdrawal processing\n";
                $benefits .= "• Higher daily limits\n";
                $benefits .= "• VIP customer service\n";
                break;
            case 3:
                $benefits .= "• 💎 All previous benefits\n";
                $benefits .= "• Premium trading features\n";
                $benefits .= "• Exclusive VIP events\n";
                $benefits .= "• Personal account manager\n";
                break;
            case 4:
                $benefits .= "• 👑 Maximum VIP privileges\n";
                $benefits .= "• Instant withdrawals\n";
                $benefits .= "• Unlimited daily limits\n";
                $benefits .= "• Exclusive premium features\n";
                $benefits .= "• Direct line to management\n";
                break;
        }

        return $benefits;
    }

    /**
     * Create the keyboard for VIP status view
     *
     * @return array The inline keyboard array
     */
    private function createVipStatusKeyboard(): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => '⭐ Buy Stars',
                        'callback_data' => 'show_star_shop',
                    ],
                    [
                        'text' => '💎 Premium Shop',
                        'callback_data' => 'show_premium_shop',
                    ],
                ],
                [
                    [
                        'text' => '💰 Wallet',
                        'callback_data' => 'show_wallet',
                    ],
                    [
                        'text' => '📊 Deposit',
                        'callback_data' => 'show_deposit',
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];
    }

    /**
     * Send error message when VIP status display fails
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @return void
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ An error occurred while loading your VIP status. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
