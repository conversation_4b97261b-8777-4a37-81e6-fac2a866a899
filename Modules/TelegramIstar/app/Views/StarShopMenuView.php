<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Components\BackToMenu;
use Modules\TelegramIstar\Services\StarShopService;

class StarShopMenuView implements ViewInterface
{
    /**
     * Handle the star shop menu display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        $username = $params[0] ?? null;

        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing star shop menu to user '.$user->tele_id);

            $description = $this->getStarShopDescription($username);

            $keyboard = $this->createStarShopKeyboard($username, $user);

            DiscordLogHelper::log('Sending star shop menu to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('StarShopMenuView failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $view = new FallbackView;
            $view->show($chatId, $user);
        }
    }

    /**
     * Get the star shop menu description
     */
    private function getStarShopDescription(?string $username): string
    {
        $tonHelper = new TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();

        // Initialize text variable
        $text = '';

        if ($username) {
            $text = "💰 Gift Stars for @$username:\n\n";
        }

        $text .= "💰 Choose a Star Package:\n\n"
            .'💎 Current TON Rate: $'.number_format($tonRate, 4)."\n"
            ."🔥 Payment method: TON only\n\n"
            .'Select a package below:';

        return $text;
    }

    /**
     * Create the inline keyboard for star shop menu
     */
    private function createStarShopKeyboard(?string $username): array
    {
        $inlineKeyboard = [];

        $tonRate = (new TonHelper)->fetchTonPrice();
        $starShopService = app(StarShopService::class);
        $starPackages = $starShopService->getStarPackages();

        // Create package buttons
        foreach ($starPackages as $package) {
            $tonPrice = number_format($package['usd_price'] / $tonRate, 4);
            $buttonText = $package['stars'].'⭐ - '.$tonPrice.' TON ($'.$package['usd_price'].')';

            $callbackData = 'buy_stars-'.$package['stars'];
            if ($username) {
                $callbackData .= '-'.$username;
            }
            $inlineKeyboard[] = [
                [
                    'text' => $buttonText,
                    'callback_data' => $callbackData,
                ],
            ];
        }

        // Add back to menu button
        $inlineKeyboard[] = [
            BackToMenu::make(),
        ];

        return [
            'inline_keyboard' => $inlineKeyboard,
        ];
    }
}
