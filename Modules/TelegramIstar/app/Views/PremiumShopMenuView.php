<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Services\PremiumService;
use Modules\TelegramIstar\Services\ThemeService;

class PremiumShopMenuView implements ViewInterface
{
    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the Telegram Premium shop menu display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        $username = $params[0] ?? null; // For gifting to others

        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing Telegram Premium shop menu to user '.$user->tele_id);

            $tonHelper = new TonHelper;
            $tonRate = $tonHelper->fetchTonPrice();

            if (!$tonRate) {
                $botClient->sendMessage(
                    $chatId,
                    "❌ TON rate not available. Please try again later.",
                    ['parse_mode' => 'Markdown']
                );
                return;
            }

            $description = $this->buildCompleteDescription($username, $tonRate);
            $keyboard = $this->createPremiumShopKeyboard($user, $username, $tonRate);

            DiscordLogHelper::log('Sending Telegram Premium shop menu to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('PremiumShopMenuView failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $view = new FallbackView;
            $view->show($chatId, $user);
        }
    }

    /**
     * Build complete description with pricing breakdown
     */
    private function buildCompleteDescription(?string $username, float $tonRate): string
    {
        $header = $username
            ? "🎁 **Telegram Premium Gift Shop**\n👤 Recipient: @{$username}\n\n"
            : "🔷 Buy Telegram Premium with TON 🔷\n\n";

        $description = $header
            . "Live TON price: {$tonRate} USD\n\n"
            . "Select a premium package:\n\n";

        $premiumService = app(PremiumService::class);
        $premiumPackages = $premiumService->getPremiumPackages();

        // Add pricing breakdown for each package
        foreach ($premiumPackages as $package) {
            $tonPrice = ceil(($package['usd_price'] / $tonRate) * 100) / 100;
            $description .= "• \${$package['usd_price']} (≈{$tonPrice} TON)\n";
        }

        return $description;
    }

    /**
     * Get the Telegram Premium shop description
     */
    private function getPremiumShopDescription(?string $username): string
    {
        $tonHelper = new TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();

        if (!$tonRate) {
            return "❌ TON rate not available. Please try again later.";
        }

        return $this->buildCompleteDescription($username, $tonRate);
    }

    /**
     * Create the inline keyboard for Telegram Premium shop menu
     */
    private function createPremiumShopKeyboard(TelegramUser $user, ?string $username, ?float $tonRate = null): array
    {
        $inlineKeyboard = [];

        if (!$tonRate) {
            $tonHelper = new TonHelper;
            $tonRate = $tonHelper->fetchTonPrice();
        }

        if (!$tonRate) {
            return ['inline_keyboard' => []];
        }

        $premiumService = app(PremiumService::class);
        $premiumPackages = $premiumService->getPremiumPackages();

        // Create package buttons
        $buttons = [];
        foreach ($premiumPackages as $package) {
            $periodText = $premiumService->getPeriodText($package['days']);

            $buttons[] = [
                'text' => "{$periodText} (\${$package['usd_price']})",
                'callback_data' => $username
                    ? "buy_premium-{$package['days']}-{$username}"
                    : "buy_premium-{$package['days']}",
            ];
        }

        // Arrange buttons in two-column layout (row_width=2)
        for ($i = 0; $i < count($buttons); $i += 2) {
            if ($i + 1 < count($buttons)) {
                $inlineKeyboard[] = [$buttons[$i], $buttons[$i + 1]];
            } else {
                $inlineKeyboard[] = [$buttons[$i]];
            }
        }

        // Add back button
        $inlineKeyboard[] = [
            [
                'text' => '🔙 Back',
                'callback_data' => 'show_premium_shop'
            ]
        ];

        return ['inline_keyboard' => $inlineKeyboard];
    }
}
