<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Illuminate\Pagination\LengthAwarePaginator;
use <PERSON><PERSON><PERSON>\TelegramIstar\Views\FallbackView;
use Mo<PERSON>les\TelegramIstar\Enums\WithdrawalType;

class PendingWithdrawalsListView implements ViewInterface
{
    /**
     * Handle the pending withdrawals list display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient();

            DiscordLogHelper::log('Showing pending withdrawals list to user ' . $user->tele_id);

            /** @var LengthAwarePaginator $withdrawals */
            $withdrawals = $params['withdrawals'];
            $currentPage = $params['current_page'];
            $totalPages = $params['total_pages'];

            $message = $this->buildWithdrawalsMessage($withdrawals, $currentPage, $totalPages);
            $keyboard = $this->buildNavigationKeyboard($currentPage, $totalPages);

            DiscordLogHelper::log('Sending pending withdrawals list to user ' . $user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

        } catch (\Exception $e) {
            DiscordLogHelper::error('PendingWithdrawalsListView failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $view = new FallbackView();
            $view->show($chatId, $user);
        }
    }

    /**
     * Build the withdrawals list message
     */
    private function buildWithdrawalsMessage(LengthAwarePaginator $withdrawals, int $currentPage, int $totalPages): string
    {
        $message = "📋 **Pending Withdrawals** 📋\n\n";

        if ($withdrawals->isEmpty()) {
            $message .= "❌ No pending withdrawal requests found.\n\n";
            return $message;
        }

        $message .= "📊 **Page {$currentPage} of {$totalPages}**\n";
        $message .= "📈 **Total Pending:** {$withdrawals->total()}\n\n";

        // Table header
        $message .= "```\n";
        $message .= sprintf("%-4s %-8s %-15s %-10s %-12s\n", "ID", "Status", "Description", "Amount", "Date");
        $message .= sprintf("%-4s %-8s %-15s %-10s %-12s\n", "----", "--------", "---------------", "----------", "------------");
        $message .= "```\n\n";

        // Table rows
        foreach ($withdrawals as $withdrawal) {
            $description = $this->getWithdrawalTypeDisplay($withdrawal->type);
            $amount = number_format($withdrawal->amount, 2);
            $status = ucfirst($withdrawal->status);
            $date = $this->formatRelativeTime($withdrawal->updated_at);

            $message .= "```\n";
            $message .= sprintf("%-4s %-8s %-15s %-10s %-12s\n",
                '#' . $withdrawal->id,
                substr($status, 0, 8),
                substr($description, 0, 15),
                $amount,
                $date
            );
            $message .= "```\n";
        }

        $message .= "\n💡 *Use the buttons below to navigate between pages.*";

        return $message;
    }

    /**
     * Build navigation keyboard
     */
    private function buildNavigationKeyboard(int $currentPage, int $totalPages): array
    {
        $keyboard = ['inline_keyboard' => []];

        // Navigation buttons row
        $navRow = [];

        if ($currentPage > 1) {
            $navRow[] = [
                'text' => '⬅️ Previous',
                'callback_data' => 'pending_withdrawals-' . ($currentPage - 1),
            ];
        }

        // Page indicator
        $navRow[] = [
            'text' => "📄 {$currentPage}/{$totalPages}",
            'callback_data' => 'pending_withdrawals-' . $currentPage, // Refresh current page
        ];

        if ($currentPage < $totalPages) {
            $navRow[] = [
                'text' => 'Next ➡️',
                'callback_data' => 'pending_withdrawals-' . ($currentPage + 1),
            ];
        }

        $keyboard['inline_keyboard'][] = $navRow;

        // Back to menu button
        $keyboard['inline_keyboard'][] = [
            [
                'text' => '🔙 Back to Menu',
                'callback_data' => 'start',
            ],
        ];

        return $keyboard;
    }

    /**
     * Get withdrawal type display name
     */
    private function getWithdrawalTypeDisplay(string $type): string
    {
        return match ($type) {
            WithdrawalType::EXCHANGE_STAR => 'Star to TON Exchange',
            WithdrawalType::STAR_PURCHASE => 'Star Purchase',
            WithdrawalType::PREMIUM_PURCHASE => 'Premium Purchase',
            WithdrawalType::STAR_WITHDRAWAL => 'Star Withdrawal',
            WithdrawalType::TON_WITHDRAWAL => 'TON Withdrawal',
            default => ucfirst(str_replace('_', ' ', $type)),
        };
    }

    /**
     * Format date as relative time
     */
    private function formatRelativeTime(\Carbon\Carbon $date): string
    {
        $now = now();
        $diffInMinutes = $now->diffInMinutes($date, false);
        $diffInHours = $now->diffInHours($date, false);
        $diffInDays = $now->diffInDays($date, false);

        if ($diffInMinutes < 0) {
            // Future date (shouldn't happen, but handle gracefully)
            return $date->format('M d');
        }

        if ($diffInMinutes < 60) {
            // Less than 1 hour
            return $diffInMinutes <= 1 ? '1 min ago' : $diffInMinutes . ' min ago';
        }

        if ($diffInHours < 24) {
            // Less than 1 day
            return $diffInHours . 'h ago';
        }

        if ($diffInDays < 30) {
            // Less than 30 days
            return $diffInDays . 'd ago';
        }

        // More than 30 days
        $diffInMonths = floor($diffInDays / 30);
        return $diffInMonths . 'mo ago';
    }
}
