<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Services\ThemeService;
use Modules\TelegramIstar\Helpers\IstarHelper;

class StartMenuView implements ViewInterface
{
    protected $mainMenuService;

    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the /start command
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient;

            $currentTheme = $this->themeService->getCurrentTheme();

            DiscordLogHelper::log('Current theme: '.DiscordLogHelper::formatJson($currentTheme));

            // Format the welcome message with user data
            $vipInfo = (new IstarHelper())->getVipInfo($user);

            DiscordLogHelper::log('VIP info: '.DiscordLogHelper::formatJson($vipInfo));

            $welcomeMessage = $this->themeService->formatDescription(
                $currentTheme['description_template'],
                [
                    'balance' => $user->balance_star,
                    'balance_ton' => $user->balance_ton,
                    'vip_emoji' => $vipInfo['vip_emoji'],
                    'vip_level' => $vipInfo['vip_level'],
                    'available' => $user->availableBalance('STAR'),
                ]
            );

            // Create inline keyboard from theme configuration
            $keyboard = $this->themeService->createInlineKeyboard($currentTheme['buttons']);

            DiscordLogHelper::log('Sending welcome message to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $welcomeMessage,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('StartCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $view = new FallbackView();
            $view->show($chatId, $user);
        }
    }
}
