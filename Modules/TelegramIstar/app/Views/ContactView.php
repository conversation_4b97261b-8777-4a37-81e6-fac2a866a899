<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use App\Services\SettingsService;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramClient\Services\TelegramBotClient;

class ContactView implements ViewInterface
{
    /**
     * Display the 24/7 support contact information
     *
     * @param  string|int  $chatId  The Telegram chat ID
     * @param  TelegramUser  $user  The user to show contact info for
     * @param  array|null  $params  Additional parameters
     * @return void
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient();

            DiscordLogHelper::log('Displaying contact information for user ' . $user->tele_id);

            $contactInfo = $this->getContactInformation();
            $message = $this->createContactMessage($contactInfo);
            $keyboard = $this->createContactKeyboard($contactInfo);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

        } catch (\Exception $e) {
            DiscordLogHelper::error('ContactView failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
        }
    }

    /**
     * Get contact information from settings
     *
     * @return array Contact information with support and official channel links
     */
    private function getContactInformation(): array
    {
        try {
            $settingsService = new SettingsService();

                $settings = $settingsService->getSettingsByKeys([
                'telegram_isar_support_channel',
                'telegram_isar_official_channel',
            ]);

            return [
                'support_channel' => $this->extractChannelFromUrl($settings['telegram_isar_support_channel']),
                'official_channel' => $this->extractChannelFromUrl($settings['telegram_isar_official_channel']),
                'support_url' => $settings['telegram_isar_support_channel'],
                'official_url' => $settings['telegram_isar_official_channel'],
            ];

        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to retrieve contact settings: ' . $e->getMessage());

            return [
                'support_channel' => 'support',
                'official_channel' => 'official',
                'support_url' => 'https://t.me/support',
                'official_url' => 'https://t.me/official',
            ];
        }
    }

    private function extractChannelFromUrl(string $url): string
    {
        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['path'])) {
            return ltrim($parsedUrl['path'], '/');
        }
        return 'channel';
    }

    /**
     * Create the contact information message
     *
     * @param  array  $contactInfo  The contact information array
     * @return string The formatted contact message
     */
    private function createContactMessage(array $contactInfo): string
    {
        $message = "📞 *24/7 Support Contact*\n\n";
        $message .= "Need help? Our support team is here for you anytime!\n\n";
        $message .= "🔹 *Support:* @{$contactInfo['support_channel']}\n";
        $message .= "🔹 *Official Channel:* @{$contactInfo['official_channel']}\n\n";
        $message .= "Feel free to reach out—we're available 24/7! 🌟";

        return $message;
    }

    /**
     * Create the contact keyboard with URL buttons
     *
     * @param  array  $contactInfo  The contact information array
     * @return array The inline keyboard array
     */
    private function createContactKeyboard(array $contactInfo): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💬 Contact Support',
                        'url' => $contactInfo['support_url'],
                    ],
                    [
                        'text' => '📢 Join Our Channel',
                        'url' => $contactInfo['official_url'],
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];
    }

    /**
     * Send error message when contact display fails
     *
     * @param  string|int  $chatId  The chat ID to send message to
     * @return void
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ An error occurred while loading contact information. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
