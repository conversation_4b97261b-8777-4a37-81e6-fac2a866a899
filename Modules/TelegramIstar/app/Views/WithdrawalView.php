<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use App\Models\Setting;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageStateService;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use App\Helpers\TonHelper;

class WithdrawalView implements ViewInterface
{
    /**
     * Show the withdrawal interface
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient();

            // Get user's star balance
            $starBalance = $user->getBalance('STAR');

            // Get exchange rate from settings (default: 100 Star = 1 USD)
            $exchangeRate = $this->getExchangeRate();

            // Calculate TON equivalent (assuming 1 USD = some TON rate)
            $tonHelper = new TonHelper();
            $tonRate = $tonHelper->fetchTonPrice();

            $message = $this->buildWithdrawalMessage($starBalance, $exchangeRate, $tonRate);
            $keyboard = $this->buildWithdrawalKeyboard($user);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            // Set user state to await withdrawal amount input
            $stateService = new MessageStateService();
            $stateService->setUserState($user, 'withdrawal_amount');

        } catch (\Exception $e) {
            DiscordLogHelper::error('WithdrawalView failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            // Send fallback message
            $this->sendFallbackMessage($chatId);
        }
    }

    /**
     * Build the withdrawal message
     */
    private function buildWithdrawalMessage(float $starBalance, float $exchangeRate, float $tonRate): string
    {
        $formattedBalance = number_format($starBalance, 2);
        $usdEquivalent = $starBalance * $exchangeRate;
        $tonEquivalent = $usdEquivalent / $tonRate;

        return "⭐ **Exchange your star balance to TON** ⭐\n\n"
            . "💰 **Your Star Balance:** {$formattedBalance} ⭐\n"
            . "💵 **USD Equivalent:** \$" . number_format($usdEquivalent, 2) . "\n"
            . "💎 **TON Equivalent:** " . number_format($tonEquivalent, 4) . " TON\n\n"
            . "📊 **Current Exchange Rate:**\n"
            . "• 1 Star = \$" . number_format($exchangeRate, 4) . " USD\n"
            . "• 1 USD = " . number_format(1/$tonRate, 4) . " TON\n\n"
            . "💡 **How it works:**\n"
            . "1. Enter the amount of stars you want to exchange\n"
            . "2. Confirm the transaction details\n"
            . "3. Your stars will be deducted immediately\n"
            . "4. TON will be sent to your wallet after admin approval\n\n"
            . "📝 **Enter the amount of stars you want to exchange:**\n"
            . "*(Maximum: {$formattedBalance} stars)*";
    }

    /**
     * Build the withdrawal keyboard
     */
    private function buildWithdrawalKeyboard(TelegramUser $user): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => '❌ Cancel',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];
    }

    /**
     * Get exchange rate from settings
     */
    private function getExchangeRate(): float
    {
        $rate = Setting::get('star_exchange_rate', 0.01);
        return (float) $rate;
    }

    /**
     * Send fallback message when view fails
     */
    private function sendFallbackMessage(string|int $chatId): void
    {
        try {
            $botClient = new TelegramBotClient();

            $message = "❌ Sorry, we're experiencing technical difficulties with the withdrawal system. Please try again later.";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 Back to Menu',
                            'callback_data' => 'start',
                        ],
                    ],
                ],
            ];

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $fallbackError) {
            DiscordLogHelper::error('Fallback message also failed: ' . $fallbackError->getMessage());
        }
    }
}
