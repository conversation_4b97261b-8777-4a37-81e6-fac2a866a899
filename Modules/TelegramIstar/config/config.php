<?php

return [
    'name' => 'TelegramIstar',

    /*
    |--------------------------------------------------------------------------
    | Telegram Istar Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the TelegramIstar module. This extends the main
    | telegram configuration with module-specific settings.
    |
    */

    // Bot configuration
    'bot' => [
        'token' => env('TELEGRAM_ISTAR_BOT_TOKEN'),
        'username' => env('TELEGRAM_ISTAR_BOT_USERNAME'),
        'name' => env('TELEGRAM_ISTAR_BOT_NAME', 'Istar Bot'),
    ],

    // Basic message templates (non-theme specific)
    'messages' => [
        'help' => "Available commands:\n/start - Start the bot\n/help - Show this help message\n/status - Check your mining status",
        'unknown_command' => 'Sorry, I don\'t understand that command. Type /help for available commands.',
        'error' => 'An error occurred while processing your request. Please try again later.',
    ],

    // Commands configuration
    'commands' => [
        'start' => [
            'description' => 'Start the bot',
            'enabled' => true,
        ],
        'help' => [
            'description' => 'Show help message',
            'enabled' => true,
        ],
        'status' => [
            'description' => 'Check mining status',
            'enabled' => true,
        ],
    ],

    // UI configuration
    'ui' => [
        'mini_app_url' => env('TELEGRAM_ISTAR_MINI_APP_URL'),
        'channel_url' => env('TELEGRAM_ISTAR_CHANNEL_URL', 'https://t.me/NiTon_News'),
        'how_to_boost_url' => env('TELEGRAM_ISTAR_HOW_TO_BOOST_URL', 'https://t.me/NiTon_News/72'),
        'official_channel' => env('TELEGRAM_ISTAR_OFFICIAL_CHANNEL', 'NiTon_News'),
    ],

    // Theme selection (used as fallback if user has no preference)
    'default_theme' => env('TELEGRAM_ISTAR_DEFAULT_THEME', 'original'),
];
