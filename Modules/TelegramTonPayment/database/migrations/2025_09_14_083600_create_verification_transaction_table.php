<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verification_transaction', function (Blueprint $table) {
            $table->id();
            $table->string('verification_code');
            $table->string('nano_amount'); // Amount in nanoTON for verification
            $table->unsignedBigInteger('user_id');
            $table->string('status')->default('pending'); // pending, verified, expired
            $table->smallInteger('attempts')->default(0);
            $table->timestamp('expires_at');
            $table->text('notes')->nullable();
            $table->string('address')->nullable(); // TON address for verification
            $table->json('metadata')->nullable(); // Additional data

            $table->index('verification_code');
            $table->index('user_id');
            $table->index('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('verification_transaction');
    }
};
