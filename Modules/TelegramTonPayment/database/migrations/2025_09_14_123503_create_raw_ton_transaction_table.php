<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('raw_ton_transaction', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_hash')->unique();
            $table->string('ton_amount_nano');
            $table->string('message')->nullable();
            $table->string('receiver_address');
            $table->string('lt')->nullable();
            $table->string('status')->default('pending');
            $table->timestamps();

            $table->index('message');
            $table->index('transaction_hash');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('raw_ton_transaction');
    }
};
