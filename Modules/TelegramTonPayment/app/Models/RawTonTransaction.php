<?php

namespace Modules\TelegramTonPayment\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\TelegramTonPayment\Database\Factories\RawTonTransactionFactory;

class RawTonTransaction extends Model
{
    use HasFactory;

    protected $table = 'raw_ton_transaction';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'transaction_hash',
        'ton_amount_nano',
        'message',
        'receiver_address',
        'lt',
        'status',
    ];

    // protected static function newFactory(): RawTonTransactionFactory
    // {
    //     // return RawTonTransactionFactory::new();
    // }
}
