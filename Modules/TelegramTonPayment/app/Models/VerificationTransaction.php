<?php

namespace Modules\TelegramTonPayment\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Model representing a verification transaction for TON payments
 *
 * @property int $id
 * @property string $verification_code
 * @property int $user_id
 * @property string $status
 * @property int $attempts
 * @property \Illuminate\Support\Carbon $expires_at
 * @property string $notes
 * @property array $metadata
 * @property int $nano_amount
 * @property string $address
 */

class VerificationTransaction extends Model
{
    use HasFactory;

    public $table = 'verification_transaction';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'verification_code',
        'user_id',
        'status',
        'attempts',
        'expires_at',
        'notes',
        'metadata',
        'nano_amount',
        'address',
    ];

    protected $casts = [
        'nano_amount' => 'string', // Cast nano_amount as string to preserve large numbers
    ];
}
