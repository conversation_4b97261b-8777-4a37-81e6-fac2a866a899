<?php

namespace Modules\TelegramTonPayment\Providers;

use App\Listeners\AfterTonPaymentSucceededListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\TelegramTonPayment\Events\AfterTonPaymentSucceeded;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event handler mappings for the application.
     *
     * @var array<string, array<int, string>>
     */
    protected $listen = [
        AfterTonPaymentSucceeded::class => [
            AfterTonPaymentSucceededListener::class,
        ],
    ];

    /**
     * Indicates if events should be discovered.
     *
     * @var bool
     */
    protected static $shouldDiscoverEvents = true;

    /**
     * Configure the proper event listeners for email verification.
     */
    protected function configureEmailVerification(): void {}
}
