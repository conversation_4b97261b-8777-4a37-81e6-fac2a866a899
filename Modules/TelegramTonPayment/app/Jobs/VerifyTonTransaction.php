<?php

namespace Modules\TelegramTonPayment\Jobs;

use App\Helpers\DiscordLogHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\TelegramTonPayment\Events\TonTransactionVerified;
use Modules\TelegramTonPayment\Jobs\ScanTonTransaction;
use Modules\TelegramTonPayment\Models\RawTonTransaction;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class VerifyTonTransaction implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    protected $address;

    public function __construct()
    {
    }

    public function handle(): void {
        VerificationTransaction::where('expires_at', '<', now())
            ->where('status', 'pending')
            ->update(['status' => 'expired']);

        VerificationTransaction::where('attempts', '>=', 5)
            ->where('status', 'pending')
            ->update(['status' => 'failed']);

        $transactions = VerificationTransaction::where('status', 'pending')
                            ->where('created_at', '>=', now()->subMinutes(3))
                            ->orderBy('created_at', 'asc')
                            ->limit(100)
                            ->get();

        $currentAddress = $transactions[0]->address;

        ScanTonTransaction::dispatch($currentAddress);

        $failedIds = [];

        foreach ($transactions as $transaction) {
            if ($transaction->address !== $currentAddress) {
                // run in next job
                continue;
            }

            $code = $transaction->verification_code;
            $raw = RawTonTransaction::where('message', $code)
                ->where('receiver_address', $transaction->address)
                ->where('status', 'pending')
                ->first();

            if ($raw) {
                if (abs($raw->ton_amount_nano - $transaction->nano_amount) > 1000000) {
                    // Amount mismatch more than 0.001 TON
                    DiscordLogHelper::error('Ton verify', [
                        'message' => 'Amount mismatch',
                        'expected' => $transaction->nano_amount,
                        'actual' => $raw->ton_amount_nano,
                        'transaction_id' => $transaction->id,
                        'raw_transaction_id' => $raw->id,
                    ]);
                    $transaction->status = 'invalid_amount';
                    $transaction->save();
                    continue;
                }

                $transaction->status = 'completed';
                $transaction->save();

                $raw->status = 'completed';
                $raw->save();

                TonTransactionVerified::dispatch($transaction->id);
            } else {
                $failedIds[] = $transaction->id;
            }
        }

        if (!empty($failedIds)) {
            VerificationTransaction::whereIn('id', $failedIds)
                ->increment('attempts');
        }
    }
}
