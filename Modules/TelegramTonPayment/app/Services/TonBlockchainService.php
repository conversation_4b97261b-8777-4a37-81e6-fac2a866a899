<?php

namespace Modules\TelegramTonPayment\Services;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TonBlockchainService
{
    private string $apiUrl;

    private string $apiKey;

    public function __construct()
    {
        $this->apiUrl = config('telegramtonpayment.ton_api_url');
        $this->apiKey = config('telegramtonpayment.ton_api_key');
    }

    /**
     * Build transaction message for TON payment
     */
    public function buildMessage(string $transactionHash): string
    {
        $prefix = config('telegramtonpayment.message_prefix', 'miner_sc_');

        return $prefix.$transactionHash;
    }

    /**
     * Convert USD to nanoTON
     */
    public function convertUsdToNanoTon(float $amountInUsd): ?int
    {
        try {
            $tonToUsdRate = $this->fetchTonPrice();

            if (is_null($tonToUsdRate) || $tonToUsdRate == 0) {
                Log::error('Could not retrieve a valid TON to USD rate from CoinGecko.');

                return null;
            }

            $amountInTon = $amountInUsd / $tonToUsdRate;
            $nanoTon = $amountInTon * 1000000000;

            return (int) $nanoTon;

        } catch (\Exception $e) {
            Log::error('Exception occurred during USD to TON conversion', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Convert nanoTON to USD
     */
    public function convertNanoTonToUsd(int $nanoTon): ?float
    {
        try {
            $tonToUsdRate = $this->fetchTonPrice();

            if (is_null($tonToUsdRate) || $tonToUsdRate == 0) {
                Log::error('Could not retrieve a valid TON to USD rate from CoinGecko.');

                return null;
            }

            $amountInTon = $nanoTon / 1000000000;
            $amountInUsd = $amountInTon * $tonToUsdRate;

            return round($amountInUsd, 2);

        } catch (\Exception $e) {
            Log::error('Exception occurred during nano TON to USD conversion', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Fetch current TON price from CoinGecko
     */
    public function fetchTonPrice(): ?float
    {
        return Cache::remember('ton_price_usd', 15, function () {
            $response = Http::withHeaders(['accept' => 'application/json'])
                ->get('https://api.coingecko.com/api/v3/simple/price', [
                    'ids' => 'the-open-network',
                    'vs_currencies' => 'usd',
                ]);

            if ($response->failed()) {
                throw new \Exception('Failed to retrieve TON to USD rate from CoinGecko.');
            }

            $data = $response->json();
            $tonToUsdRate = Arr::get($data, 'the-open-network.usd');

            return $tonToUsdRate;
        });
    }

    /**
     * Check if a transaction is successful based on blockchain data
     */
    public function isTransactionSuccess(array $transaction): bool
    {
        $description = $transaction['description'];

        if ($description['aborted'] && ! $description['credit_first']) {
            return false;
        }

        if (! Arr::get($transaction, 'compute_ph.skiped') && ! in_array(Arr::get($transaction, 'compute_ph.exit_code'), [0, 1])) {
            return false;
        }

        if (Arr::get($transaction, 'action') && (! Arr::get($transaction, 'action.success') || Arr::get($transaction, 'action.result_code') !== 0)) {
            return false;
        }

        return true;
    }

    public function fetchTransaction(string $address)
    {
        try {
            // Build the API endpoint URL
            $endpoint = rtrim($this->apiUrl, '/').'/api/v3/transactions';

            // Default parameters
            $queryParams = [
                'account' => $address,
                'limit' => 100,
                'sort' => 'desc',
            ];

            $response = Http::withHeaders([
                'X-API-Key' => $this->apiKey,
                'Accept' => 'application/json',
            ])->get($endpoint, $queryParams);

            // Check if the request was successful
            if ($response->successful()) {
                return $response->json()['transactions'];
            } else {
                // Log the error
                Log::error('TON API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                return null;
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred during TON transaction fetch', [
                'address' => $address,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function parseTransaction(array $transaction): array
    {
        return [
            'hash' => $transaction['hash'],
            'value' => intval(Arr::get($transaction, 'in_msg.value', 0)),
            'message' => Arr::get($transaction, 'in_msg.message_content.decoded.comment', ''),
            'lt'
        ];
    }

    /**
     * Validate TON transaction by message and amount
     */
    public function validateTransactionByMessage(string $address, string $expectedMessage, int $nanoAmountTonNeeded): bool
    {
        try {
            $transactions = $this->fetchTransaction($address);

            // Check if the request was successful
            if ($transactions) {
                foreach ($transactions as $transaction) {
                    if ($this->isTransactionSuccess($transaction)) {
                        $comment = Arr::get($transaction, 'in_msg.message_content.decoded.comment', '');
                        $receivedAmount = intval(Arr::get($transaction, 'in_msg.value', 0));

                        if ($comment == $expectedMessage && $receivedAmount == $nanoAmountTonNeeded) {
                            return true;
                        }
                    }
                }

                return false;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred during TON transaction validation', [
                'expected_message' => $expectedMessage,
                'expected_amount' => $nanoAmountTonNeeded,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Check for fraud by comparing expected and actual prices
     */
    public function checkOrderFraud(int $nanoTonValue, float $expectedPriceUsd): ?bool
    {
        $threshold = 0.2; // $0.2 threshold constant

        // Convert nano TON amount to USD
        $actualPriceUsd = $this->convertNanoTonToUsd($nanoTonValue);

        if ($actualPriceUsd === null) {
            Log::error('Could not convert nano TON to USD for fraud check', [
                'nano_ton_value' => $nanoTonValue,
            ]);

            return null; // Could not perform fraud check
        }

        $difference = abs($actualPriceUsd - $expectedPriceUsd);

        Log::info('Fraud check performed', [
            'expected_price_usd' => $expectedPriceUsd,
            'actual_price_usd' => $actualPriceUsd,
            'difference' => $difference,
            'threshold' => $threshold,
            'is_fraud' => $difference > $threshold,
        ]);

        return $difference > $threshold;
    }
}
