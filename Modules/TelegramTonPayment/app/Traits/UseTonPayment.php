<?php

namespace Modules\TelegramTonPayment\Traits;

use Modules\TelegramTonPayment\Models\VerificationTransaction;
use Illuminate\Support\Str;

trait UseTonPayment
{
    public function createVerificationTransaction(string $address, string $nanoAmount, ?array $metadata = []): VerificationTransaction
    {
        $expirationMinutes = config('telegramtonpayment.expiration_minutes', 60);
        $code = $this->buildVerificationCode();

        return $this->hasManyVerificationTransactions()->create([
            'verification_code' => $code,
            'expires_at' => now()->addMinutes($expirationMinutes),
            'address' => $address,
            'metadata' => json_encode($metadata),
            'nano_amount' => $nanoAmount,
        ]);
    }

    public function hasManyVerificationTransactions()
    {
        return $this->hasMany(VerificationTransaction::class, 'user_id', 'id');
    }

    public function buildVerificationCode()
    {
        $identifyField = config('telegramtonpayment.identify_field');

        $identifier = $this->{$identifyField} ?? 'user';

        return "tx_{$identifier}_" . Str::random(8);
    }
}
