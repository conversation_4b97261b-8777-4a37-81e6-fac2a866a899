<?php

namespace Modules\TelegramTonPayment\Console;

use Illuminate\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramBot;
use Modules\TelegramTonPayment\Jobs\VerifyTonTransaction;
use App\Helpers\DiscordLogHelper;

class VerifyTonTransactionCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ton:verify-transaction';

    /**
     * The console command description.
     */
    protected $description = 'Verify TON transactions.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle() {
        DiscordLogHelper::log('Scan TON transactions started', true);

        TelegramBot::query()->cursor()->each(function (TelegramBot $tenant) {
            $tenant->makeCurrent();
            VerifyTonTransaction::dispatch();
        });
    }
}
