<?php

namespace Modules\TelegramTonPayment\Console;

use Illuminate\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramBot;
use Modules\TelegramTonPayment\Jobs\ScanTonTransaction;
use App\Helpers\DiscordLogHelper;

class ScanTonTransactionCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ton:scan-transaction';

    /**
     * The console command description.
     */
    protected $description = 'Scan TON transactions.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle() {
        DiscordLogHelper::log('Scan TON transactions started', true);

        TelegramBot::query()->cursor()->each(function (TelegramBot $tenant) {
            $tenant->makeCurrent();
            ScanTonTransaction::dispatch();
        });
    }
}
