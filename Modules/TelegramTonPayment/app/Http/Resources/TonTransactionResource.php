<?php

namespace Modules\TelegramTonPayment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TonTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'transaction_hash' => $this->transaction_hash,
            'message' => $this->message,
            'receiver_address' => $this->receiver_address,
            'ton_amount' => $this->ton_amount_decimal,
            'status' => $this->status->value,
        ];
    }
}
