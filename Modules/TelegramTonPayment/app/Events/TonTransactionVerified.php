<?php

namespace Modules\TelegramTonPayment\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class TonTransactionVerified
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public VerificationTransaction $transaction;

    /**
     * Create a new event instance.
     */
    public function __construct(int $transactionId)
    {
        $this->transaction = VerificationTransaction::find($transactionId);
    }
}
