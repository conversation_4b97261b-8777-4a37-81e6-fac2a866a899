<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = $this->getDefaultSettings();

        foreach ($settings as $setting) {
            $this->createSettingIfNotExists($setting);
        }

        $this->command->info('Settings seeded successfully!');
    }

    /**
     * Create setting only if it doesn't exist
     */
    private function createSettingIfNotExists(array $setting): void
    {
        $existingSetting = Setting::where('key', $setting['key'])->first();

        if (! $existingSetting) {
            Setting::create($setting);
            $this->command->info("Created setting: {$setting['key']}");
        } else {
            $existingSetting->update($setting);
            $this->command->info("Updated setting: {$setting['key']}");
        }
    }

    /**
     * Get default settings to be seeded
     */
    private function getDefaultSettings(): array
    {
        return [
            [
                'key' => 'ton_wallet_address',
                'value' => 'UQALKPk4q6mOGYCQq8ujB1ne4BDQixoG1RZRruodP-AKBu6R',
                'type' => 'string',
                'description' => 'Ton wallet receive address',
            ],
            [
                'key' => 'star_conversion_rate',
                'value' => 0.013,
                'type' => 'number',
                'description' => 'Star to USDT conversion rate',
            ],
            [
                'key' => 'star_exchange_rate',
                'value' => 0.01,
                'type' => 'number',
                'description' => 'Buy star from User at this rate (USD per Star)',
            ],
            [
                'key' => 'telegram_isar_official_channel',
                'value' => 'https://t.me/StarBankGlobal',
                'type' => 'string',
                'description' => 'Official Telegram channel link',
            ],
            [
                'key' => 'telegram_isar_support_channel',
                'value' => 'https://t.me/Sellstarglobal',
                'type' => 'string',
                'description' => 'Support Telegram channel link',
            ]
        ];
    }
}
