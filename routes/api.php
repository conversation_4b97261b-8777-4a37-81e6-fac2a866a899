<?php

use App\Http\Controllers\Api\BoosterController;
use App\Http\Controllers\Api\LeaderBoardController;
use App\Http\Controllers\Api\MiningController;
use App\Http\Controllers\Api\SettingController;
use App\Http\Controllers\Api\StaffAuthController;
use App\Http\Controllers\Api\StarController;
use App\Http\Controllers\Api\TonPaymentController;
use App\Http\Controllers\Api\TransactionController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\WithdrawalController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// =============================================================================
// PUBLIC ROUTES - No authentication required
// =============================================================================

Route::prefix('public')->group(function () {
    // API Health Check
    Route::get('/health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now(),
            'service' => 'backend-api',
        ]);
    });

    // Public settings
    Route::get('/client-settings', [SettingController::class, 'getPublic']);
});

// TON price route moved to TelegramTonPayment module
// Route::get('/ton-price', [TonPaymentController::class, 'tonPrice']);

// =============================================================================
// STAFF ROUTES - Staff authentication
// =============================================================================

Route::prefix('staff')->group(function () {
    // Public staff routes (no authentication required)
    Route::prefix('auth')->group(function () {
        Route::post('/login', [StaffAuthController::class, 'login']);
    });

    // Protected staff routes (authentication required)
    Route::middleware('auth:staff')->group(function () {
        Route::prefix('auth')->group(function () {
            Route::post('/logout', [StaffAuthController::class, 'logout']);
        });
        Route::get('/me', [StaffAuthController::class, 'me']);
    });
});

Route::prefix('users')->group(function () {
    Route::post('/sync', [UserController::class, 'sync']); // Legacy endpoint
});

// =============================================================================
// USER ROUTES - Protected user endpoints
// =============================================================================

Route::prefix('user')->middleware('user.auth')->group(function () {
    // User profile
    Route::get('/me', [UserController::class, 'me']);

    Route::post('/boost', [BoosterController::class, 'boost']);

    // Mining
    Route::post('/mining/claimed', [MiningController::class, 'claimed']);

    // Transactions
    Route::get('/transactions', [TransactionController::class, 'userList']);

    // Withdrawals
    Route::post('/withdrawals', [WithdrawalController::class, 'create']);
    Route::get('/withdrawals', [WithdrawalController::class, 'userList']);
    Route::post('/withdrawals/{id}/cancel', [WithdrawalController::class, 'cancel']);

    // Leaderboard
    Route::get('/leaderboard/speed', [LeaderBoardController::class, 'topSpeed']);
    Route::get('/leaderboard/balance', [LeaderBoardController::class, 'topBalance']);
    Route::get('/leaderboard/my-rank', [LeaderBoardController::class, 'userRank']);

    Route::post('/star-to-ton', [StarController::class, 'starToTon']);
});

// =============================================================================
// ADMIN ROUTES - Admin authentication required
// =============================================================================

Route::prefix('admin')->middleware('admin.auth')->group(function () {
    // Users
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'list']);
        Route::post('/block', [UserController::class, 'blockUser']);
        Route::post('/unblock', [UserController::class, 'unblockUser']);
        Route::get('/blocked', [UserController::class, 'getBlockedUsers']);
    });

    // Settings Management
    Route::prefix('settings')->group(function () {
        Route::get('/', [SettingController::class, 'index']);
        Route::get('/{key}', [SettingController::class, 'show']);
        Route::put('/{key}', [SettingController::class, 'update']);
    });

    // Transactions Management
    Route::get('/transactions', [TransactionController::class, 'adminList']);

    // Withdrawals Management
    Route::get('/withdrawals', [WithdrawalController::class, 'adminList']);
    Route::post('/withdrawals/{id}/approve', [WithdrawalController::class, 'approve']);
    Route::post('/withdrawals/{id}/reject', [WithdrawalController::class, 'reject']);
});
