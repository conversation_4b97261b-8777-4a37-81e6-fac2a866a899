<?php

namespace App\Listeners;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Modules\TelegramTonPayment\Events\AfterTonPaymentSucceeded;

class AfterTonPaymentSucceededListener
{
    public function handle(AfterTonPaymentSucceeded $event)
    {
        try {
            $tonTransaction = $event->transaction;

            if (! $tonTransaction) {
                Log::error('TON transaction not found in AfterTonPaymentSucceeded event');

                return;
            }

            $user = User::find($tonTransaction->user_id);

            if (! $user) {
                Log::error('User not found for TON transaction', [
                    'transaction_id' => $tonTransaction->id,
                    'user_id' => $tonTransaction->user_id,
                ]);

                return;
            }

            Transaction::create([
                'user_id' => $user->id,
                'value' => $tonTransaction->ton_amount_nano, // Store in nanoTON for consistency
                'type' => TransactionType::TON_DEPOSIT,
                'status' => TransactionStatus::COMPLETED,
                'description' => sprintf(
                    'TON payment completed: %s TON (Hash: %s)',
                    number_format($tonTransaction->ton_amount_decimal, 6),
                    $tonTransaction->transaction_hash
                ),
                'hash' => Transaction::generateHash(),
            ]);

            $user->addTonBalance($tonTransaction->ton_amount_decimal);

        } catch (\Exception $e) {
            Log::error('Error processing TON payment success event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $event->transaction?->id,
            ]);

            throw $e;
        }
    }
}
