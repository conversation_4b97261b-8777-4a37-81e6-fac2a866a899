<?php

namespace App\Jobs;

use App\Helpers\DiscordLogHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class QueueTestJob implements ShouldQueue, NotTenantAware
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Send a test message to Discord using DiscordLogHelper
        DiscordLogHelper::error('🧪 Queue Test Job Executed Successfully', [
            'test_type' => 'queue_system_test',
            'timestamp' => now()->toISOString(),
            'queue_connection' => config('queue.default'),
            'job_class' => self::class,
            'message' => 'This is a test message to verify that the job queue system is working correctly and can send notifications to Discord.',
            'status' => 'success',
            'executed_at' => now()->format('Y-m-d H:i:s T'),
        ]);
    }
}
