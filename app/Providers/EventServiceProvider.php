<?php

namespace App\Providers;

use App\Listeners\AfterStarPaymentSucceededListener;
use App\Listeners\AfterTonPaymentSucceededListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\TelegramStarPayment\Events\AfterStarPaymentSucceeded;
use Modules\TelegramTonPayment\Events\AfterTonPaymentSucceeded;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event handler mappings for the application.
     *
     * @var array<string, array<int, string>>
     */
    protected $listen = [
        AfterStarPaymentSucceeded::class => [
            AfterStarPaymentSucceededListener::class,
        ],
        AfterTonPaymentSucceeded::class => [
            AfterTonPaymentSucceededListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
