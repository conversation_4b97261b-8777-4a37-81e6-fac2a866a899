<?php

namespace App\Http\Controllers\Api;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Services\BoostService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BoosterController extends Controller
{
    protected $tonHelper;

    protected $boostService;

    public function __construct(BoostService $boostService)
    {
        $this->boostService = $boostService;
    }

    public function boost(Request $request)
    {
        $request->validate([
            'ton_amount' => 'required|numeric|min:3',
        ]);

        $user = $request->user();
        $tonAmount = $request->ton_amount;

        try {
            DB::transaction(function () use ($user, $tonAmount) {
                $user->reduceTonBalance($tonAmount);

                Transaction::create([
                    'value' => $tonAmount,
                    'user_id' => $user->id,
                    'amount' => $tonAmount,
                    'type' => TransactionType::BOOST_PAYMENT,
                    'status' => TransactionStatus::COMPLETED,
                ]);

                $this->boostService->applyBoost($user, $tonAmount);
            });

            return response()->json(['message' => 'Boost applied successfully.', 'success' => true]);
        } catch (\Throwable $th) {
            Log::error('Failed to apply boost: '.$th->getMessage());

            return response()->json([
                'message' => 'Failed to apply boost. Please try again later.',
                'error' => $th->getMessage(),
            ], 500);
        }

    }
}
