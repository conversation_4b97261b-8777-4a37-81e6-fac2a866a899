<?php

namespace App\Http\Controllers\Api;

use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Helpers\TonHelper;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TonPaymentController extends Controller
{
    protected $tonHelper;

    protected $orderService;

    public function __construct(TonHelper $tonHelper)
    {
        $this->tonHelper = $tonHelper;
    }

    public function getMessage(Request $request)
    {
        $user_id = $request->user()->id;
        $nanoTon = $request->ton * 1000_000_000;

        $existingTransaction = Transaction::where('user_id', $user_id)
            ->where('value', $nanoTon)
            ->where('type', TransactionType::BOOST_PAYMENT)
            ->where('status', TransactionStatus::PENDING)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->first();

        if ($existingTransaction) {
            $transaction = $existingTransaction;

            Transaction::where('user_id', $user_id)
                ->where('type', TransactionType::BOOST_PAYMENT)
                ->where('status', TransactionStatus::PENDING)
                ->where('id', '!=', $existingTransaction->id)
                ->delete();
        } else {
            $transaction = Transaction::create([
                'user_id' => $user_id,
                'type' => TransactionType::BOOST_PAYMENT,
                'hash' => Str::random(16),
                'status' => TransactionStatus::PENDING,
                'description' => 'Ton payment',
                'value' => $nanoTon,
            ]);
        }

        $message = $this->tonHelper->buildMessage($transaction->hash);

        return response()->json([
            'success' => true,
            'message' => 'Message built successfully',
            'data' => [
                'message' => $message,
                'transaction_hash' => $transaction->hash,
                'address' => env('TON_RECEIVER_ADDRESS'),
            ],
        ], 200);
    }

    public function complete(Request $request)
    {
        $user_id = $request->user()->id;
        $hash = $request->hash;

        $transaction = Transaction::where('user_id', $user_id)
            ->where('type', TransactionType::BOOST_PAYMENT)
            ->where('hash', $hash)
            ->where('status', TransactionStatus::PENDING)
            ->first();

        if (! $transaction) {
            return response()->json([
                'success' => false,
                'message' => 'No pending transaction found',
            ], 404);
        }

        $isSuccess = $this->tonHelper->checkTransaction(
            env('TON_RECEIVER_ADDRESS'),
            $transaction
        );

        if ($isSuccess) {
            try {
                DB::beginTransaction();

                $transaction->update([
                    'status' => TransactionStatus::COMPLETED,
                    'description' => $transaction->description.' - Completed',
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Transaction completed successfully and order created.',
                    'data' => [
                        'transaction_hash' => $transaction->hash,
                        'status' => $transaction->status,
                    ],
                ], 200);

            } catch (\Exception $e) {
                DB::rollBack();

                Log::error('Failed to create order after TON payment confirmation.', [
                    'transaction_id' => $transaction->id,
                    'error' => $e->getMessage(),
                ]);

                // Even if order creation fails, we should probably mark the transaction as failed
                // to avoid repeated processing attempts. Or maybe a new status 'processing_failed'.
                // For now, returning a server error seems appropriate.
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction confirmed on blockchain, but failed to create order.',
                ], 500);
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found on blockchain or failed',
                'data' => [
                    'transaction_hash' => $transaction->hash,
                    'status' => $transaction->status,
                ],
            ], 400);
        }
    }
}
