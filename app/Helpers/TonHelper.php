<?php

namespace App\Helpers;

use App\Services\SettingsService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TonHelper
{
    private $apiUrl;

    private $apiKey;

    public function __construct()
    {
        $this->apiUrl = env('TON_API_URL');
        $this->apiKey = env('TON_API_KEY');
    }

    public function buildMessage($txhash)
    {
        return 'miner_sc_'.$txhash;
    }

    public function convertUsdToNanoTon($amountInUsd)
    {
        try {
            $tonToUsdRate = $this->fetchTonPrice();

            if (is_null($tonToUsdRate) || $tonToUsdRate == 0) {
                Log::error('Could not retrieve a valid TON to USD rate from CoinGecko.');

                return null;
            }

            $amountInTon = $amountInUsd / $tonToUsdRate;
            $nanoTon = $amountInTon * 1000000000;

            return (int) $nanoTon;

        } catch (\Exception $e) {
            Log::error('Exception occurred during USD to TON conversion', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function convertStarToNanoTon($amountStar)
    {
        $settingService = app(SettingsService::class);
        $starToUsdRate = $settingService->getSetting('star_conversion_rate');

        $amountInUsd = $amountStar * $starToUsdRate;
        $nanoTon = $this->convertUsdToNanoTon($amountInUsd);

        return (int) $nanoTon;
    }

    public function fetchTonPrice()
    {
        return Cache::remember('ton_price_usd', 15, function () {
            $response = Http::withHeaders(['accept' => 'application/json'])
                ->get('https://api.coingecko.com/api/v3/simple/price', [
                    'ids' => 'the-open-network',
                    'vs_currencies' => 'usd',
                ]);

            if ($response->failed()) {
                throw new \Exception('Failed to retrieve TON to USD rate from CoinGecko.');
            }

            $data = $response->json();

            $tonToUsdRate = Arr::get($data, 'the-open-network.usd');

            return $tonToUsdRate;
        });
    }

    public function isTransactionSuccess($transaction)
    {
        $description = $transaction['description'];

        if ($description['aborted'] && ! $description['credit_first']) {
            return false;
        }

        if (! Arr::get($transaction, 'compute_ph.skiped') && ! in_array(Arr::get($transaction, 'compute_ph.exit_code'), [0, 1])) {
            return false;
        }

        if (Arr::get($transaction, 'action') && (! Arr::get($transaction, 'action.success') || Arr::get($transaction, 'action.result_code') !== 0)) {
            return false;
        }

        return true;
    }

    public function checkTransaction($address, $transactionDatabase)
    {
        $hash = $transactionDatabase['hash'];
        $nanoAmountTonNeeded = $transactionDatabase['value'];
        $message = $this->buildMessage($hash);

        return $this->validateTransactionByMessage($address, $message, $nanoAmountTonNeeded);
    }

    /**
     * Generic method to validate TON transactions by message and amount
     */
    public function validateTransactionByMessage($address, $expectedMessage, $nanoAmountTonNeeded)
    {
        try {
            // Build the API endpoint URL
            $endpoint = rtrim($this->apiUrl, '/').'/api/v3/transactions';

            // Default parameters
            $queryParams = [
                'account' => $address,
                'limit' => 100,
                'sort' => 'desc',
            ];

            $response = Http::withHeaders([
                'X-API-Key' => $this->apiKey,
                'Accept' => 'application/json',
            ])->get($endpoint, $queryParams);

            // Check if the request was successful
            if ($response->successful()) {
                foreach ($response->json()['transactions'] as $transaction) {
                    if ($this->isTransactionSuccess($transaction)) {
                        $comment = Arr::get($transaction, 'in_msg.message_content.decoded.comment', '');
                        $receivedAmount = intval(Arr::get($transaction, 'in_msg.value', 0));

                        if ($comment == $expectedMessage && $receivedAmount == $nanoAmountTonNeeded) {
                            return true;
                        }
                    }
                }

                return false;
            } else {
                // Log the error
                Log::error('TON API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred during TON transaction validation', [
                'expected_message' => $expectedMessage,
                'expected_amount' => $nanoAmountTonNeeded,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Extract transaction amounts from a TON transaction
     * Returns amounts in nanoTON
     */
    public function extractTransactionAmounts($transaction) {}

    public function convertNanoTonToUsd($nanoTon)
    {
        try {
            $tonToUsdRate = $this->fetchTonPrice();

            if (is_null($tonToUsdRate) || $tonToUsdRate == 0) {
                Log::error('Could not retrieve a valid TON to USD rate from CoinGecko.');

                return null;
            }

            $amountInTon = $nanoTon / 1000000000;
            $amountInUsd = $amountInTon * $tonToUsdRate;

            return round($amountInUsd, 2);

        } catch (\Exception $e) {
            Log::error('Exception occurred during nano TON to USD conversion', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function checkOrderFraud($transaction, $expectedPriceUsd)
    {
        $threshold = 0.2; // $0.2 threshold constant

        // Convert nano TON amount to USD
        $actualPriceUsd = $this->convertNanoTonToUsd($transaction->value);

        if ($actualPriceUsd === null) {
            Log::error('Could not convert nano TON to USD for fraud check', [
                'transaction_id' => $transaction->id,
                'nano_ton_value' => $transaction->value,
            ]);

            return null; // Could not perform fraud check
        }

        $difference = abs($actualPriceUsd - $expectedPriceUsd);

        Log::info('Fraud check performed', [
            'transaction_id' => $transaction->id,
            'expected_price_usd' => $expectedPriceUsd,
            'actual_price_usd' => $actualPriceUsd,
            'difference' => $difference,
            'threshold' => $threshold,
            'is_fraud' => $difference > $threshold,
        ]);

        return $difference > $threshold;
    }
}
