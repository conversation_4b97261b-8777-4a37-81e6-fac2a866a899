<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BoostService
{
    const GAS_PER_SECOND = 1000000000;

    /**
     * Apply speed boost to user based on TON amount with accumulation logic
     */
    public function applyBoost(User $user, float $tonAmount): bool
    {
        DB::beginTransaction();

        try {
            $this->saveCheckpoint($user);

            // Get current user data
            $currentSpeed = $user->speed ?? 0;
            $currentBoostExpiredAt = $user->boost_expired_at;

            // Calculate accumulated speed using the new logic
            $speedData = $this->calculateAccumulatedSpeed(
                $tonAmount,
                $currentSpeed,
                $currentBoostExpiredAt
            );

            // Set new expiration date (30 days from now)
            $expiredDate = Carbon::now()->addDays(30);

            // Update user with accumulated speed
            $user->update([
                'speed' => $speedData['final_speed'],
                'boost_expired_at' => $expiredDate,
            ]);

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollback();

            return false;
        }
    }

    /**
     * Save checkpoint and calculate actual coin mined by old speed
     */
    private function saveCheckpoint(User $user, $time = null): void
    {
        $now = $time ?? Carbon::now();
        $coinsMined = $this->calculatePendingTokens($user, $now);

        $user->update([
            'pending_token' => $user->pending_token + $coinsMined,
            'last_checkpoint' => $now,
        ]);
    }

    /**
     * Calculate remaining days from current boost expiration
     */
    private function calculateRemainingDays(?Carbon $boostExpiredAt): float
    {
        if (! $boostExpiredAt || $boostExpiredAt->isPast()) {
            return 0;
        }

        return Carbon::now()->diffInSeconds($boostExpiredAt, false) / (24 * 60 * 60);
    }

    /**
     * Calculate accumulated speed based on new TON amount and existing boost
     * Logic: new_speed + (old_speed * remaining_days / 30)
     */
    private function calculateAccumulatedSpeed(
        float $newTonAmount,
        float $currentSpeed,
        ?Carbon $boostExpiredAt
    ): array {
        // Calculate new speed from TON amount
        $newSpeed = $this->calculateSpeedFromTon($newTonAmount);

        // Calculate remaining days from current boost
        $remainingDays = $this->calculateRemainingDays($boostExpiredAt);

        // Adjust old speed based on remaining days
        $adjustedOldSpeed = $remainingDays > 0
            ? $currentSpeed * ($remainingDays / 30)
            : 0;

        // Calculate final accumulated speed
        $finalSpeed = $newSpeed + $adjustedOldSpeed;

        return [
            'new_speed' => $newSpeed,
            'remaining_days' => $remainingDays,
            'adjusted_old_speed' => $adjustedOldSpeed,
            'final_speed' => $finalSpeed,
        ];
    }

    /**
     * Calculate actual speed from TON amount
     * TODO: Implement actual calculation logic
     */
    private function calculateSpeedFromTon(float $tonAmount): float
    {
        $convertRate = Setting::get('convert_rate');
        $monthProfit = $convertRate * $tonAmount;
        $secProfit = ($monthProfit * self::GAS_PER_SECOND) / (30 * 24 * 60 * 60);

        return $secProfit;
    }

    /**
     * Check if user's boost has expired and reset speed if needed
     */
    public function processBoostExpiration(User $user): bool
    {
        // If no boost or boost hasn't expired yet, skip
        if (! $user->boost_expired_at || $user->boost_expired_at->isFuture()) {
            return false;
        }

        // Boost has expired, save checkpoint up to expiration time
        $this->saveCheckpoint($user, $user->boost_expired_at);

        // Reset speed to base speed and clear boost expiration
        $user->update([
            'speed' => $this->getBaseSpeed(),
            'boost_expired_at' => null,
        ]);

        return true;
    }

    /**
     * Get base mining speed for users
     * TODO: Make this configurable via settings
     */
    private function getBaseSpeed(): float
    {
        $baseSpeed = Setting::get('base_speed');

        return $baseSpeed;
    }

    /**
     * Calculate pending tokens for user without updating database
     */
    public function calculatePendingTokens(User $user, $endTime = null): float
    {
        if ($user->speed == 0) {
            return 0;
        }

        $now = $endTime ?? Carbon::now();
        $lastCheckpoint = $user->last_checkpoint ?? $now;

        // Calculate time elapsed since last checkpoint in seconds
        $secondsElapsed = $lastCheckpoint->diffInSeconds($now);
        if ($secondsElapsed <= 0) {
            return 0;
        }

        // Calculate coins mined
        $coinsMined = $user->speed * $secondsElapsed / self::GAS_PER_SECOND;

        return $coinsMined;
    }

    /**
     * Get boost status for user
     */
    public function getBoostStatus(User $user): array
    {
        $remainingDays = $this->calculateRemainingDays($user->boost_expired_at);

        return [
            'current_speed' => $user->speed ?? 0,
            'boost_expired_at' => $user->boost_expired_at,
            'remaining_days' => $remainingDays,
            'is_active' => $remainingDays > 0,
        ];
    }

    /**
     * Process mining for user (can be called by scheduled job)
     */
    public function processMining(User $user): array
    {
        $this->processBoostExpiration($user);

        $coinsMined = $this->calculatePendingTokens($user);

        if ($coinsMined > 0) {
            $user->update([
                'pending_token' => $user->pending_token + $coinsMined,
                'last_checkpoint' => Carbon::now(),
            ]);

            return [
                'success' => true,
                'coins_mined' => $coinsMined,
            ];
        }

        return [
            'success' => false,
            'message' => 'No mining progress since last checkpoint',
        ];
    }
}
