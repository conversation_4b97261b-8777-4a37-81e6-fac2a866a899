<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Database\Eloquent\Model;

class SettingsService
{
    protected $settingModel;

    public function __construct($settingModel = null)
    {
        $this->settingModel = $settingModel ?: new Setting();
    }

    /**
     * Get settings by array of keys
     */
    public function getSettingsByKeys(array $keys): array
    {
        $settings = $this->settingModel::whereIn('key', $keys)->get();

        $result = [];
        foreach ($keys as $key) {
            $setting = $settings->where('key', $key)->first();
            $result[$key] = $setting ? $setting->cast_value : null;
        }

        return $result;
    }

    public function getSetting(string $key): ?string
    {
        $setting = $this->settingModel::where('key', $key)->first();

        if (is_null($setting)) {
            throw new \Exception("Setting not set: {$key}");
        }

        return $setting->cast_value;
    }
}
