<?php

namespace App\Console\Commands;

use App\Jobs\QueueTestJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;

class TestQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the job queue system by dispatching a test job to verify queue processing and Discord notifications';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Starting Queue System Test...');
        $this->newLine();

        // Display current queue configuration
        $queueConnection = config('queue.default');
        $this->info("📋 Current queue connection: {$queueConnection}");
        
        try {
            // Dispatch the test job
            $this->info('📤 Dispatching test job to queue...');
            QueueTestJob::dispatch();
            
            $this->newLine();
            $this->info('✅ Test job has been successfully dispatched to the queue!');
            $this->newLine();
            
            $this->info('📝 What to expect:');
            $this->info('   • The job will be processed by your queue worker');
            $this->info('   • A test message will be sent to Discord via DiscordLogHelper');
            $this->info('   • Check your Discord channel for a test error message');
            $this->info('   • The message will be clearly marked as a test');
            $this->newLine();
            
            $this->info('🔍 To monitor the queue:');
            $this->info('   • Check queue status: php artisan queue:work --once');
            $this->info('   • View failed jobs: php artisan queue:failed');
            $this->info('   • Monitor queue: php artisan queue:monitor');
            $this->newLine();
            
            $this->info('💡 If you don\'t see the Discord message:');
            $this->info('   • Ensure your queue worker is running');
            $this->info('   • Check that APP_DEBUG is true or the job uses persist=true');
            $this->info('   • Verify Discord webhook URL in DiscordLogHelper');
            $this->info('   • Check the failed_jobs table for any failures');
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to dispatch test job: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
